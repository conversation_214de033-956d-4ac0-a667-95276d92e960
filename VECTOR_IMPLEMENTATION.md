# Vector Implementation for RAG (Retrieval-Augmented Generation)

## Overview

You're absolutely correct! The `embedding_vector` column should use the proper PostgreSQL `vector` type from the pgvector extension for optimal RAG performance. I've implemented a solution that leverages the full power of pgvector while working around TypeORM's limitations.

## Implementation Details

### 1. Database Level (PostgreSQL + pgvector)

**Actual Column Type**: `vector(1536)`
```sql
-- The column is created as proper vector type in PostgreSQL
ALTER TABLE requirements 
ADD COLUMN embedding_vector vector(1536);

-- Supports all pgvector operations:
-- Cosine similarity: <=>
-- L2 distance: <->  
-- Inner product: <#>
```

**Vector Operations Available**:
```sql
-- Find similar requirements using cosine similarity
SELECT *, 1 - (embedding_vector <=> '[0.1,0.2,0.3,...]') as similarity
FROM requirements 
WHERE embedding_vector IS NOT NULL
ORDER BY embedding_vector <=> '[0.1,0.2,0.3,...]'
LIMIT 10;

-- <PERSON><PERSON> optimized indexes for fast similarity search
CREATE INDEX ON requirements USING ivfflat (embedding_vector vector_cosine_ops);
```

### 2. TypeORM Level (Application)

**Challenge**: TypeORM doesn't natively recognize the `vector` type from pgvector extension.

**Solution**: Custom transformer that handles conversion between JavaScript arrays and PostgreSQL vector format.

```typescript
// Entity definition
@Column({
  type: 'text', // TypeORM sees it as text, but PostgreSQL handles it as vector
  transformer: new VectorTransformer(),
  nullable: true,
  name: 'embedding_vector'
})
embeddingVector: number[]; // JavaScript side: number array
```

### 3. Vector Transformer

**Purpose**: Seamlessly converts between JavaScript `number[]` and PostgreSQL `vector` format.

```typescript
export class VectorTransformer implements ValueTransformer {
  // Database → JavaScript: "[1.0,2.0,3.0]" → [1.0, 2.0, 3.0]
  from(value: any): number[] | null {
    if (!value) return null;
    const cleanValue = value.replace(/[\[\]]/g, '');
    return cleanValue.split(',').map(num => parseFloat(num.trim()));
  }

  // JavaScript → Database: [1.0, 2.0, 3.0] → "[1.0,2.0,3.0]"
  to(value: any): string | null {
    if (!value || !Array.isArray(value)) return null;
    return `[${value.join(',')}]`;
  }
}
```

## RAG Benefits

### 1. **Native Vector Operations**
- **Cosine Similarity**: Perfect for semantic similarity
- **L2 Distance**: Euclidean distance for spatial relationships  
- **Inner Product**: Dot product for relevance scoring

### 2. **Optimized Indexing**
```sql
-- IVFFlat index for approximate nearest neighbor search
CREATE INDEX idx_requirements_embedding_vector 
ON requirements USING ivfflat (embedding_vector vector_cosine_ops) 
WITH (lists = 100);

-- HNSW index for even faster searches (PostgreSQL 14+)
CREATE INDEX idx_requirements_embedding_hnsw
ON requirements USING hnsw (embedding_vector vector_cosine_ops);
```

### 3. **High Performance**
- **Sub-millisecond searches** on millions of vectors
- **Approximate nearest neighbor** algorithms
- **Memory-efficient** storage and operations

## Usage Examples

### 1. Storing Embeddings
```typescript
// Service automatically converts number[] to vector format
await this.requirementRepository.update(requirementId, {
  embeddingVector: [0.1, 0.2, 0.3, ...], // 1536 dimensions
});
```

### 2. Similarity Search
```typescript
// Find similar requirements
const similar = await this.embeddingService.searchSimilarRequirements(
  queryVector,     // number[] - your search vector
  10,             // limit - number of results
  0.7             // threshold - minimum similarity score
);
```

### 3. Raw SQL for Complex Queries
```typescript
// Advanced similarity search with metadata filtering
const query = `
  SELECT 
    r.*,
    1 - (r.embedding_vector <=> $1::vector) as similarity
  FROM requirements r
  WHERE r.embedding_vector IS NOT NULL
    AND r.metadata->>'processingMethod' = 'embedding_based'
    AND 1 - (r.embedding_vector <=> $1::vector) > $2
  ORDER BY r.embedding_vector <=> $1::vector
  LIMIT $3
`;

const results = await this.repository.query(query, [
  `[${queryVector.join(',')}]`, // Vector literal format
  threshold,
  limit
]);
```

## Vector Dimensions

### Supported Models & Dimensions
```typescript
// OpenAI Models
text-embedding-ada-002: 1536 dimensions
text-embedding-3-small: 1536 dimensions  
text-embedding-3-large: 3072 dimensions

// Google Models
Gemini embedding: 768 dimensions
Universal Sentence Encoder: 512 dimensions

// Adjust in migration:
ALTER TABLE requirements 
ADD COLUMN embedding_vector vector(1536); -- Change dimension as needed
```

## Performance Optimization

### 1. Index Configuration
```sql
-- Tune IVFFlat lists parameter based on data size
-- Rule of thumb: lists = sqrt(total_rows)
CREATE INDEX ... WITH (lists = 1000); -- For ~1M rows
```

### 2. Query Optimization
```sql
-- Use EXPLAIN ANALYZE to optimize queries
EXPLAIN ANALYZE 
SELECT * FROM requirements 
ORDER BY embedding_vector <=> '[...]' 
LIMIT 10;
```

### 3. Memory Settings
```sql
-- PostgreSQL configuration for vector operations
shared_preload_libraries = 'vector'
work_mem = '256MB'  -- Increase for large vector operations
```

## Migration Strategy

### 1. Enable pgvector Extension
```sql
-- Run as superuser
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. Add Vector Column
```sql
-- Run the provided migration
\i src/migrations/001-add-vector-and-token-usage.sql
```

### 3. Verify Installation
```sql
-- Check extension
SELECT * FROM pg_extension WHERE extname = 'vector';

-- Check column type
\d requirements
-- Should show: embedding_vector | vector(1536) | 
```

## Best Practices

### 1. **Normalize Vectors**
```typescript
// Ensure vectors are normalized for cosine similarity
const normalized = vector.map(v => v / magnitude);
```

### 2. **Batch Operations**
```typescript
// Use batch updates for multiple embeddings
await this.repository.save(requirements); // Batch save
```

### 3. **Monitor Performance**
```sql
-- Monitor index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes 
WHERE tablename = 'requirements';
```

### 4. **Error Handling**
```typescript
// Handle vector dimension mismatches
try {
  await this.repository.update(id, { embeddingVector: vector });
} catch (error) {
  if (error.message.includes('dimension')) {
    throw new Error('Vector dimension mismatch');
  }
}
```

## Testing Vector Operations

### 1. **Similarity Search Test**
```bash
# Upload documents and test similarity
curl -X POST "/api/v1/requirements/search/semantic" \
  -d '{"query": "user authentication", "limit": 5}'
```

### 2. **Performance Test**
```sql
-- Test query performance
EXPLAIN ANALYZE 
SELECT COUNT(*) FROM requirements 
WHERE embedding_vector <=> '[0.1,0.2,...]' < 0.5;
```

### 3. **Index Effectiveness**
```sql
-- Check if index is being used
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM requirements 
ORDER BY embedding_vector <=> '[...]' 
LIMIT 10;
```

## Conclusion

This implementation gives you the **best of both worlds**:

✅ **Full pgvector power** - Native vector operations, optimized indexes, high performance
✅ **TypeORM compatibility** - Seamless integration with your existing ORM setup  
✅ **Developer experience** - Work with familiar `number[]` arrays in TypeScript
✅ **RAG ready** - Optimized for semantic search and retrieval-augmented generation

The vector column is now properly configured for high-performance RAG applications while maintaining clean, type-safe code in your NestJS application! 🚀
