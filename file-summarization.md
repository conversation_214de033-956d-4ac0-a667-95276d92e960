import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TokenUsage, TokenType } from '../../entities';

interface SummarizationResult {
  summary: string;
  tokensUsed: number;
  model: string;
  provider: string;
  cost?: number;
  hasImages: boolean;
  hasDiagrams: boolean;
  hasComplexLayout: boolean;
  detectedElements: string[];
}

@Injectable()
export class FileSummarizationService {
  private readonly logger = new Logger(FileSummarizationService.name);
  private readonly llmProvider: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    this.llmProvider = this.configService.get<string>('LLM', 'OPENAI');
  }

  async summarizeFile(
    fileBuffer: Buffer,
    mimetype: string,
    originalFilename: string,
    requirementId: string
  ): Promise<SummarizationResult> {
    try {
      this.logger.log(`Starting file summarization for ${originalFilename} using ${this.llmProvider}`);

      // Extract basic text content first
      const extractedText = await this.extractTextFromFile(fileBuffer, mimetype, originalFilename);

      // Generate summary using LLM
      const result = await this.generateSummaryWithLLM(
        extractedText,
        mimetype,
        originalFilename
      );

      // Track token usage
      await this.trackTokenUsage(
        requirementId,
        'content_extraction',
        result.tokensUsed,
        result.model,
        result.provider,
        result.cost
      );

      this.logger.log(`File summarization completed for ${originalFilename}. Tokens used: ${result.tokensUsed}`);
      return result;

    } catch (error) {
      this.logger.error(`Error summarizing file ${originalFilename}:`, error);
      
      // Return fallback summary
      return await this.generateFallbackSummary(fileBuffer, mimetype, originalFilename);
    }
  }

  private async generateSummaryWithLLM(
  extractedText: string,
  mimetype: string,
  filename: string
): Promise<SummarizationResult> {
  
  // Check if this is vision-processed content
  const isVisionProcessed = extractedText.includes('Vision AI Analysis:');
  
  let prompt: string;
  if (isVisionProcessed) {
    // Use simpler prompt for vision-analyzed content
    prompt = `Summarize the following vision analysis of document "${filename}":

${extractedText}

Please provide a JSON response with summary, detected visual elements, and document characteristics.`;
  } else {
    // Use existing text-based prompt
    prompt = this.buildSummarizationPrompt(extractedText, mimetype, filename);
  }

  if (this.llmProvider.toUpperCase() === 'OPENAI') {
    return this.generateOpenAISummary(prompt);
  } else if (this.llmProvider.toUpperCase() === 'GEMINI') {
    return this.generateGeminiSummary(prompt);
  } else {
    throw new Error(`Unsupported LLM provider: ${this.llmProvider}`);
  }
}


  private buildSummarizationPrompt(text: string, mimetype: string, filename: string): string {
    return `
You are an expert document analyzer. Please analyze the following document content and provide a comprehensive summary.

Document Information:
- Filename: ${filename}
- File Type: ${mimetype}
- Content Length: ${text.length} characters

Instructions:
1. Provide a clear, concise summary of the document content (2-3 paragraphs)
2. Identify key topics, requirements, or main points
3. Analyze the document structure and visual elements
4. Determine if the document contains:
   - Images or diagrams
   - Complex layouts (tables, multi-column, etc.)
   - Technical diagrams or charts

Document Content:
${text.substring(0, 6000)} ${text.length > 6000 ? '...(truncated)' : ''}

Please respond in the following JSON format:
{
  "summary": "Detailed summary of the document content...",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "hasImages": true/false,
  "hasDiagrams": true/false,
  "hasComplexLayout": true/false,
  "detectedElements": ["images", "tables", "diagrams", "charts", "code"],
  "documentType": "requirements/specification/manual/other",
  "confidence": 0.95
}
`;
  }

  private async generateOpenAISummary(prompt: string): Promise<SummarizationResult> {
    const { OpenAI } = require('openai');

    const openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });

    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert document analyzer. Respond only with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      });

      const content = response.choices[0].message.content;
      const analysis = JSON.parse(content);

      const tokensUsed = response.usage.total_tokens;
      const costPer1kTokens = 0.03; // GPT-4 pricing
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        summary: analysis.summary,
        tokensUsed,
        model: 'gpt-4',
        provider: 'openai',
        cost,
        hasImages: analysis.hasImages || false,
        hasDiagrams: analysis.hasDiagrams || false,
        hasComplexLayout: analysis.hasComplexLayout || false,
        detectedElements: analysis.detectedElements || []
      };

    } catch (error) {
      this.logger.error('OpenAI summarization error:', error);
      throw error;
    }
  }

  private async generateGeminiSummary(prompt: string): Promise<SummarizationResult> {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    
    const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

    try {
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const content = response.text();

      // Clean the response - remove markdown code blocks if present
      const cleanContent = content.replace(/```json\s*|\s*```/g, '').trim();
      const analysis = JSON.parse(cleanContent);
      
      const tokensUsed = Math.round(content.length / 4); // Rough estimation, rounded to integer
      const costPer1kTokens = 0.00025; // Gemini Pro pricing estimate
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        summary: analysis.summary,
        tokensUsed,
        model: 'gemini-2.5-flash',
        provider: 'gemini',
        cost,
        hasImages: analysis.hasImages || false,
        hasDiagrams: analysis.hasDiagrams || false,
        hasComplexLayout: analysis.hasComplexLayout || false,
        detectedElements: analysis.detectedElements || []
      };

    } catch (error) {
      this.logger.error('Gemini summarization error:', error);
      throw error;
    }
  }

  private async extractTextFromFile(buffer: Buffer, mimetype: string, originalname: string): Promise<string> {
    try {
      // Handle text files
      if (mimetype.includes('text/') || originalname.endsWith('.md') || originalname.endsWith('.txt')) {
        return buffer.toString('utf-8');
      }

      // Handle PDF files
      if (mimetype === 'application/pdf' || originalname.toLowerCase().endsWith('.pdf')) {
        return await this.extractPdfContent(buffer, originalname);
      }

      // For other file types, return basic file info
      return `[File: ${originalname}]
File Type: ${mimetype}
File Size: ${buffer.length} bytes

This file type (${mimetype}) is not yet supported for content extraction.
Please implement specific parsing for this format.`;

    } catch (error) {
      this.logger.error(`Error extracting content from ${originalname}:`, error);
      return `[File: ${originalname}]
File Type: ${mimetype}
File Size: ${buffer.length} bytes

Error occurred during content extraction: ${error.message}`;
    }
  }

  private async extractPdfContent(buffer: Buffer, filename: string): Promise<string> {
    try {
      const pdf = require('pdf-parse');
      const data = await pdf(buffer);
      
      const extractedText = data.text || '';
      const numPages = data.numpages || 0;

      // If minimal text, process as image-heavy PDF
      if (extractedText.trim().length < 100) {
        this.logger.log(`PDF ${filename} appears image-heavy, processing with vision model`);
        return await this.processImageHeavyPDF(buffer, filename, numPages);
      }

      return `[PDF File: ${filename}]
  Pages: ${numPages}
  Content Length: ${extractedText.length} characters

  Extracted Content:
  ${extractedText}`;

    } catch (error) {
      this.logger.error(`Error parsing PDF ${filename}:`, error);
      // Fallback to image processing
      return await this.processImageHeavyPDF(buffer, filename, 0);
    }
  }

  private async processImageHeavyPDF(buffer: Buffer, filename: string, numPages: number): Promise<string> {
    try {
      // Convert PDF pages to images
      const pdfImages = await this.convertPdfToImages(buffer);
      
      // Process with vision model
      const visionAnalysis = await this.analyzeImagesWithVision(pdfImages, filename);
      
      return `[Image-Heavy PDF: ${filename}]
  Pages: ${numPages}
  Processing Method: Vision AI Analysis

  Visual Content Analysis:
  ${visionAnalysis}`;

    } catch (error) {
      this.logger.error(`Error processing image-heavy PDF ${filename}:`, error);
      return `[PDF File: ${filename}]
  Pages: ${numPages}
  Content Type: Image-heavy PDF (vision processing failed)
  Error: ${error.message}`;
    }
  }

  private async analyzeImagesWithVision(images: Buffer[], filename: string): Promise<string> {
  if (this.llmProvider.toUpperCase() === 'OPENAI') {
    return await this.analyzeWithOpenAIVision(images, filename);
  } else if (this.llmProvider.toUpperCase() === 'GEMINI') {
    return await this.analyzeWithGeminiVision(images, filename);
  }
  
  throw new Error(`Vision analysis not supported for provider: ${this.llmProvider}`);
}

private async analyzeWithOpenAIVision(images: Buffer[], filename: string): Promise<string> {
  const { OpenAI } = require('openai');
  const openai = new OpenAI({
    apiKey: this.configService.get<string>('OPENAI_API_KEY'),
  });

  // Prepare image messages
  const imageMessages = images.map((imageBuffer, index) => ({
    type: "image_url",
    image_url: {
      url: `data:image/png;base64,${imageBuffer.toString('base64')}`,
      detail: "high"
    }
  }));

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview", // or "gpt-4o" for newer model
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this PDF document "${filename}". Describe the content, identify any diagrams, charts, UI mockups, technical drawings, text content, and provide a comprehensive summary of what you can see across all pages.`
            },
            ...imageMessages
          ]
        }
      ],
      max_tokens: 1500,
    });

    return response.choices[0].message.content || "No analysis available";
    
  } catch (error) {
    this.logger.error('OpenAI Vision analysis error:', error);
    throw error;
  }
}

private async analyzeWithGeminiVision(images: Buffer[], filename: string): Promise<string> {
  const { GoogleGenerativeAI } = require('@google/generative-ai');
  
  const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));
  const model = genAI.getGenerativeModel({ model: "gemini-pro-vision" }); // or "gemini-1.5-flash" for newer model

  try {
    // Convert images to Gemini format
    const imageParts = images.map((imageBuffer, index) => ({
      inlineData: {
        data: imageBuffer.toString('base64'),
        mimeType: 'image/png'
      }
    }));

    const prompt = `Analyze this PDF document "${filename}". Describe the content, identify any diagrams, charts, UI mockups, technical drawings, text content, and provide a comprehensive summary of what you can see across all pages.`;

    const result = await model.generateContent([prompt, ...imageParts]);
    const response = await result.response;
    
    return response.text();
    
  } catch (error) {
    this.logger.error('Gemini Vision analysis error:', error);
    throw error;
  }
}


  private async convertPdfToImages(pdfBuffer: Buffer): Promise<Buffer[]> {
  const pdfjsLib = require('pdfjs-dist/legacy/build/pdf.js');
  
  const loadingTask = pdfjsLib.getDocument({
    data: new Uint8Array(pdfBuffer),
    standardFontDataUrl: 'node_modules/pdfjs-dist/standard_fonts/'
  });
  
  const pdf = await loadingTask.promise;
  const images: Buffer[] = [];
  
  // Process first few pages (limit for cost control)
  const maxPages = Math.min(pdf.numPages, 3);
  
  for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
    const page = await pdf.getPage(pageNum);
    const viewport = page.getViewport({ scale: 1.5 });
    
    // Create canvas (you'll need canvas package: npm install canvas)
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(viewport.width, viewport.height);
    const context = canvas.getContext('2d');
    
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise;
    
    // Convert to image buffer
    const imageBuffer = canvas.toBuffer('image/png');
    images.push(imageBuffer);
  }
  
  return images;
}


  private async generateFallbackSummary(
    fileBuffer: Buffer,
    mimetype: string,
    filename: string
  ): Promise<SummarizationResult> {
    const basicText = await this.extractTextFromFile(fileBuffer, mimetype, filename);

    return {
      summary: `Document analysis for ${filename} (${mimetype}). File size: ${fileBuffer.length} bytes. ${basicText.substring(0, 200)}...`,
      tokensUsed: 0,
      model: 'fallback',
      provider: 'local',
      cost: 0,
      hasImages: mimetype.includes('image') || mimetype.includes('pdf'),
      hasDiagrams: false,
      hasComplexLayout: mimetype.includes('pdf') || mimetype.includes('docx'),
      detectedElements: [mimetype.split('/')[0]]
    };
  }

  private async trackTokenUsage(
    requirementId: string,
    tokenType: string,
    tokensUsed: number,
    model: string,
    provider: string,
    cost?: number
  ): Promise<void> {
    try {
      const tokenUsage = this.tokenUsageRepository.create({
        requirementId,
        tokenType: tokenType as TokenType,
        tokensUsed,
        model,
        provider,
        cost,
        metadata: {
          timestamp: new Date().toISOString(),
          operation: 'file_summarization',
          model,
          provider
        }
      });

      await this.tokenUsageRepository.save(tokenUsage);
      this.logger.log(`Token usage tracked: ${tokensUsed} tokens for ${tokenType} using ${model}`);

    } catch (error) {
      this.logger.error(`Error tracking token usage for requirement ${requirementId}:`, error);
    }
  }
}
