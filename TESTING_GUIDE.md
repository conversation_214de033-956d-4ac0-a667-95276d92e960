# Testing Guide: Hybrid Processing & Vector Embeddings

## Prerequisites

### 1. Database Setup
Run the migration to add vector support and token usage table:

```sql
-- Run the migration file
\i src/migrations/001-add-vector-and-token-usage.sql
```

### 2. Environment Variables
Add these to your `.env` file:

```env
# LLM Provider (choose one)
LLM=GEMINI  # or OPENAI
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Embedding Provider
EMBEDDING_PROVIDER=OPENAI  # or GEMINI

# Redis for queues (if not already configured)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=15
```

### 3. Install Dependencies
If you need the AI libraries:

```bash
npm install openai @google/generative-ai
```

## Testing Scenarios

### Scenario 1: Simple Text File (Traditional Parsing)

**Test File**: Create a simple `.txt` file with plain text content.

```bash
curl -X POST "http://localhost:3000/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@simple_text.txt" \
  -F "name=Simple Text Requirement" \
  -F "user_id=test-user-uuid" \
  -F "company_id=test-company-uuid" \
  -F "project_id=test-project-uuid"
```

**Expected Result**:
- Processing method: `traditional_parse`
- Embedding status: `completed` (no embedding needed)
- Content extracted immediately

### Scenario 2: Complex PDF (Embedding Required)

**Test File**: Upload a PDF with images, diagrams, or complex layout.

```bash
curl -X POST "http://localhost:3000/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@complex_document.pdf" \
  -F "name=Complex PDF Requirement" \
  -F "user_id=test-user-uuid" \
  -F "company_id=test-company-uuid" \
  -F "project_id=test-project-uuid"
```

**Expected Result**:
- Processing method: `embedding_based` or `hybrid`
- Embedding status: `in_progress` → `completed`
- Embedding job created

### Scenario 3: Monitor Embedding Progress

After uploading a complex file, monitor the embedding progress:

```bash
# Get the requirement ID from upload response
REQUIREMENT_ID="your-requirement-id"

# Check embedding status
curl -X GET "http://localhost:3000/api/v1/requirements/${REQUIREMENT_ID}/embedding/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response**:
```json
{
  "requirementId": "requirement-uuid",
  "status": "in_progress",
  "jobId": "embedding_uuid",
  "progress": 75,
  "processingMethod": "embedding_based",
  "embeddingStatus": "in_progress"
}
```

### Scenario 4: Test Bulk Delete with Token Tracking

```bash
# First, get some requirement IDs
curl -X GET "http://localhost:3000/api/v1/requirements" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Then bulk delete
curl -X DELETE "http://localhost:3000/api/v1/requirements/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "requirementIds": ["req-id-1", "req-id-2"],
    "userId": "test-user-uuid",
    "reason": "Testing bulk delete",
    "deleteFiles": true
  }'
```

### Scenario 5: Queue Monitoring

Check queue statistics:

```bash
# Upload queue stats
curl -X GET "http://localhost:3000/api/v1/requirements/queue/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Embedding queue stats
curl -X GET "http://localhost:3000/api/v1/requirements/embeddings/queue/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Bulk delete queue stats
curl -X GET "http://localhost:3000/api/v1/requirements/bulk-delete/queue/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Database Verification

### Check Token Usage
```sql
-- View token usage for a specific requirement
SELECT * FROM token_usage WHERE requirement_id = 'your-requirement-id';

-- Get usage statistics
SELECT * FROM get_token_usage_stats();

-- Check total costs
SELECT 
  provider,
  SUM(cost) as total_cost,
  SUM(tokens_used) as total_tokens
FROM token_usage 
GROUP BY provider;
```

### Check Vector Embeddings
```sql
-- Check if embeddings were generated
SELECT 
  id,
  requirement_id,
  name,
  CASE 
    WHEN embedding_vector IS NOT NULL THEN 'Has Embedding'
    ELSE 'No Embedding'
  END as embedding_status,
  metadata->>'embeddingStatus' as status,
  metadata->>'processingMethod' as method
FROM requirements;
```

### Test Vector Search (Future)
```sql
-- Example similarity search (when implemented)
SELECT * FROM search_similar_requirements(
  '[0.1, 0.2, 0.3, ...]'::vector,  -- Query vector
  0.7,  -- Similarity threshold
  10    -- Limit
);
```

## Error Testing

### Test LLM Validation Failure
1. Set invalid API keys
2. Upload a file
3. Verify fallback behavior

### Test Embedding Generation Failure
1. Upload a file that triggers embedding
2. Simulate API failure
3. Check retry functionality

### Test Queue Failures
1. Stop Redis
2. Try uploading files
3. Restart Redis and verify recovery

## Performance Testing

### Load Testing
```bash
# Upload multiple files simultaneously
for i in {1..10}; do
  curl -X POST "http://localhost:3000/api/v1/requirements/upload/async" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    -F "file=@test_file_${i}.pdf" \
    -F "name=Load Test ${i}" \
    -F "user_id=test-user-uuid" \
    -F "company_id=test-company-uuid" \
    -F "project_id=test-project-uuid" &
done
wait
```

### Monitor Queue Performance
- Access Bull Board at `http://localhost:3000/queues`
- Monitor job processing times
- Check for failed jobs

## Validation Checklist

### ✅ Database Changes
- [ ] `token_usage` table created
- [ ] `embedding_vector` column added to requirements
- [ ] `metadata` column added to requirements
- [ ] Indexes created for performance
- [ ] Functions created for search and stats

### ✅ API Endpoints
- [ ] Upload endpoints use hybrid processing
- [ ] Embedding status endpoint works
- [ ] Embedding retry endpoint works
- [ ] Bulk delete with token tracking works
- [ ] Queue statistics endpoints work

### ✅ Processing Flow
- [ ] Simple files use traditional parsing
- [ ] Complex files trigger LLM validation
- [ ] Embedding jobs are created for complex files
- [ ] Token usage is tracked for all AI operations
- [ ] Metadata is properly stored and updated

### ✅ Error Handling
- [ ] LLM validation failures fall back gracefully
- [ ] Embedding failures are retryable
- [ ] Queue failures don't break the system
- [ ] Partial processing results are preserved

### ✅ Performance
- [ ] Simple files process immediately
- [ ] Complex files process in background
- [ ] Queue processing is efficient
- [ ] Database queries are optimized

## Troubleshooting

### Common Issues

1. **Vector column errors**: Ensure pgvector extension is installed
2. **LLM API errors**: Check API keys and rate limits
3. **Queue not processing**: Verify Redis connection
4. **Metadata not updating**: Check TypeScript types and casting

### Debug Commands

```bash
# Check application logs
docker logs your-app-container

# Check Redis queue
redis-cli -h localhost -p 6379
> KEYS *queue*
> LLEN bull:upload-queue:waiting

# Check database connections
psql -h localhost -U your_user -d requirement_service
\dt  # List tables
\d requirements  # Describe requirements table
```

## Next Steps

After successful testing:

1. **Implement semantic search** using vector embeddings
2. **Add cost monitoring dashboard** for token usage
3. **Optimize embedding models** based on usage patterns
4. **Add batch processing** for multiple files
5. **Implement caching** for similar content

## Production Considerations

1. **API Rate Limits**: Implement proper rate limiting for LLM APIs
2. **Cost Monitoring**: Set up alerts for high token usage
3. **Vector Index Tuning**: Optimize pgvector indexes for your data size
4. **Queue Scaling**: Configure multiple workers for high load
5. **Backup Strategy**: Include vector data in backup procedures
