import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmbeddingService } from '../services/embedding.service';
import { FileSummarizationService } from '../services/file-summarization.service';
import { GoogleCloudStorageService } from '../../storage/google-cloud-storage.service';
import { RequirementsService } from '../requirements.service';
import { EmbeddingJobData, FileEmbeddingJobData, EmbeddingJobResult } from '../services/embedding-queue.service';

@Injectable()
@Processor('embeddings')
export class EmbeddingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmbeddingProcessor.name);

  constructor(
    private embeddingService: EmbeddingService,
    private fileSummarizationService: FileSummarizationService,
    private googleCloudStorageService: GoogleCloudStorageService,
    private requirementsService: RequirementsService,
  ) {
    super();
  }

  async process(job: Job<EmbeddingJobData | FileEmbeddingJobData>): Promise<EmbeddingJobResult> {
    this.logger.log(`Processing embedding job ${job.id} for requirement ${job.data.requirementId}`);

    try {
      // Check job type and process accordingly
      if ('storageUrl' in job.data) {
        // File-based embedding job
        return await this.processFileEmbeddingJob(job as Job<FileEmbeddingJobData>);
      } else {
        // Text-based embedding job (legacy)
        return await this.processTextEmbeddingJob(job as Job<EmbeddingJobData>);
      }
    } catch (error) {
      this.logger.error(`Embedding job ${job.id} failed:`, error);

      const result: EmbeddingJobResult = {
        requirementId: job.data.requirementId,
        success: false,
        embeddingGenerated: false,
        tokensUsed: 0,
        message: 'Embedding generation failed',
        error: error.message
      };

      throw error; // This will mark the job as failed
    }
  }

  private async processFileEmbeddingJob(job: Job<FileEmbeddingJobData>): Promise<EmbeddingJobResult> {
    const { requirementId, storageUrl, userCredentials, jobId } = job.data;

    this.logger.log(`Processing file embedding job ${jobId} for requirement ${requirementId}`);

    // Update progress: Starting
    await job.updateProgress(10);

    // Step 1: Download file from storage
    this.logger.log(`Downloading file from storage: ${storageUrl}`);
    const fileBuffer = await this.googleCloudStorageService.downloadFile(storageUrl);

    // Get file info from storage URL
    const filename = storageUrl.split('/').pop() || 'unknown';
    const mimetype = this.getMimetypeFromFilename(filename);

    await job.updateProgress(25);

    // Step 2: Generate summary using LLM
    this.logger.log(`Generating LLM summary for file: ${filename}`);
    const summaryResult = await this.fileSummarizationService.summarizeFile(
      fileBuffer,
      mimetype,
      filename,
      requirementId
    );

    await job.updateProgress(50);

    // Step 3: Generate embedding from summary
    this.logger.log(`Generating embedding from summary for requirement: ${requirementId}`);
    const embeddingResult = await this.embeddingService.generateEmbedding(
      summaryResult.summary,
      requirementId,
      `File: ${filename} (${mimetype})`
    );

    await job.updateProgress(75);

    // Step 4: Update requirement with embedding
    this.logger.log(`Updating requirement ${requirementId} with embedding`);
    await this.embeddingService.updateRequirementEmbedding(requirementId, embeddingResult);

    // Step 5: Update content and context with enhanced content
    const currentRequirement = await this.requirementsService.findOne(requirementId);
    const enhancedContext = this.buildEnhancedContext(
      currentRequirement.content,
      currentRequirement.context,
      summaryResult
    );

    // Step 6: Build enhanced content for better AI analysis
    const enhancedContent = this.buildEnhancedContent(
      currentRequirement.content,
      summaryResult
    );

    // Step 7: Update metadata, content, and context
    const updatedMetadata = {
      ...currentRequirement.metadata,
      llmSummary: summaryResult.summary,
      hasImages: summaryResult.hasImages,
      hasDiagrams: summaryResult.hasDiagrams,
      hasComplexLayout: summaryResult.hasComplexLayout,
      detectedElements: summaryResult.detectedElements,
      embeddingStatus: 'completed',
      processingMethod: 'llm_summarization_embedding',
      lastProcessed: new Date().toISOString(),
      processedBy: userCredentials,
      contentEnhanced: true
    };

    await this.requirementsService.updateMetadata(requirementId, updatedMetadata);
    await this.requirementsService.updateContext(requirementId, enhancedContext);

    // Update content column with enhanced content for better AI analysis
    await this.requirementsService.updateContent(requirementId, enhancedContent);

    // Step 7: Track token usage for embedding
    await this.embeddingService.trackTokenUsage(
      requirementId,
      'embedding',
      embeddingResult.tokensUsed,
      embeddingResult.model,
      embeddingResult.provider,
      embeddingResult.cost
    );

    await job.updateProgress(100);

    const result: EmbeddingJobResult = {
      requirementId,
      success: true,
      embeddingGenerated: true,
      tokensUsed: embeddingResult.tokensUsed + summaryResult.tokensUsed,
      message: `File embedding generated successfully using ${embeddingResult.provider}/${embeddingResult.model}. Summary: ${summaryResult.summary.substring(0, 100)}...`
    };

    this.logger.log(`File embedding job ${jobId} completed successfully`);
    return result;
  }

  private async processTextEmbeddingJob(job: Job<EmbeddingJobData>): Promise<EmbeddingJobResult> {
    const { requirementId, content, additionalContext, jobId } = job.data;

    this.logger.log(`Processing text embedding job ${jobId} for requirement ${requirementId}`);

    // Update progress: Starting
    await job.updateProgress(10);

    // Generate embedding
    this.logger.log(`Generating embedding for requirement ${requirementId}`);
    await job.updateProgress(30);

    const embeddingResult = await this.embeddingService.generateEmbedding(
      content,
      requirementId,
      additionalContext
    );

    await job.updateProgress(70);

    // Update requirement with embedding
    this.logger.log(`Updating requirement ${requirementId} with embedding`);
    await this.embeddingService.updateRequirementEmbedding(requirementId, embeddingResult);

    await job.updateProgress(100);

    const result: EmbeddingJobResult = {
      requirementId,
      success: true,
      embeddingGenerated: true,
      tokensUsed: embeddingResult.tokensUsed,
      message: `Embedding generated successfully using ${embeddingResult.provider}/${embeddingResult.model}`
    };

    this.logger.log(`Text embedding job ${jobId} completed successfully`);
    return result;
  }

  private getMimetypeFromFilename(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();

    const mimetypeMap: Record<string, string> = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'md': 'text/markdown',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'svg': 'image/svg+xml',
      'webp': 'image/webp'
    };

    return mimetypeMap[extension || ''] || 'application/octet-stream';
  }

  private buildEnhancedContext(
    originalContent: string,
    existingContext: string | null,
    summaryResult: any
  ): string {
    const sections = [];

    // Add original content if not already in context
    if (originalContent && originalContent.trim()) {
      sections.push('=== ORIGINAL CONTENT ===');
      sections.push(originalContent.trim());
    }

    // Add existing context if present (preserve previous enhancements)
    if (existingContext && existingContext.trim() && existingContext !== originalContent) {
      sections.push('=== PREVIOUS CONTEXT ===');
      sections.push(existingContext.trim());
    }

    // Add LLM-extracted insights
    sections.push('=== LLM ANALYSIS ===');
    sections.push(`Summary: ${summaryResult.summary}`);

    if (summaryResult.hasImages) {
      sections.push('📷 Contains Images: Visual elements detected and analyzed');
    }

    if (summaryResult.hasDiagrams) {
      sections.push('📊 Contains Diagrams: Structural diagrams and flowcharts identified');
    }

    if (summaryResult.hasComplexLayout) {
      sections.push('📋 Complex Layout: Multi-column or structured layout detected');
    }

    if (summaryResult.detectedElements && summaryResult.detectedElements.length > 0) {
      sections.push(`🔍 Detected Elements: ${summaryResult.detectedElements.join(', ')}`);
    }

    sections.push(`🤖 Processed: ${new Date().toISOString()}`);

    return sections.join('\n\n');
  }

  /**
   * Build enhanced content for the content column
   * This combines original content with LLM insights for better AI analysis
   */
  private buildEnhancedContent(
    originalContent: string,
    summaryResult: any
  ): string {
    const sections = [];

    // Start with LLM summary as primary content for image-heavy files
    if (summaryResult.summary && summaryResult.summary.trim()) {
      sections.push('=== ENHANCED CONTENT (LLM ANALYSIS) ===');
      sections.push(summaryResult.summary.trim());
    }

    // Add original content if it has meaningful text
    if (originalContent && originalContent.trim() && originalContent.trim().length > 10) {
      sections.push('=== ORIGINAL PARSED TEXT ===');
      sections.push(originalContent.trim());
    }

    // Add detected visual elements information
    if (summaryResult.hasImages || summaryResult.hasDiagrams || summaryResult.hasComplexLayout) {
      const visualInfo = [];
      if (summaryResult.hasImages) visualInfo.push('📷 Contains Images');
      if (summaryResult.hasDiagrams) visualInfo.push('📊 Contains Diagrams');
      if (summaryResult.hasComplexLayout) visualInfo.push('📋 Complex Layout');

      sections.push('=== VISUAL ELEMENTS DETECTED ===');
      sections.push(visualInfo.join(', '));

      if (summaryResult.detectedElements && summaryResult.detectedElements.length > 0) {
        sections.push('🔍 Detected Elements: ' + summaryResult.detectedElements.join(', '));
      }
    }

    // Add processing timestamp
    sections.push('🤖 Enhanced: ' + new Date().toISOString());

    return sections.join('\n\n');
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Embedding job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Embedding job ${job.id} failed:`, err);
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.log(`Embedding job ${job.id} progress: ${progress}%`);
  }
}
