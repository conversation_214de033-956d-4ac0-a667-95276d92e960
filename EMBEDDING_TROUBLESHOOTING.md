# Embedding Endpoint Troubleshooting Guide

## Common Issues and Solutions

### 1. UUID Format Error

**Error:**
```json
{
  "message": "Failed to generate embedding: invalid input syntax for type uuid: \"r003\"",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** Using `requirementId` (like "r003") instead of the UUID `id` field.

**Solution:** Use the UUID from the `id` field in the requirements table:
```json
{
  "user_id": "aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6",
  "company_id": "704f4925-9bd3-46b4-8bce-28f8671ef482",
  "project_id": "f907b2b1-4347-480c-8bc5-0b669649599a",
  "id": "4be657e1-9b65-4ce0-be82-9a9dd2953841"  // ✅ Use UUID, not "r003"
}
```

### 2. Invalid User Credentials

**Error:**
```json
{
  "message": "Invalid user credentials: user_id=aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6, company_id=704f4925-9bd3-46b4-8bce-28f8671ef482, project_id=f907b2b1-4347-480c-8bc5-0b669649599a not found in users table",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** The combination of user_id, company_id, and project_id doesn't exist in the users table.

**Solution:** Verify the user credentials exist:
```sql
SELECT id, user_id, company_id, project_id
FROM users
WHERE user_id = 'aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6'
  AND company_id = '704f4925-9bd3-46b4-8bce-28f8671ef482'
  AND project_id = 'f907b2b1-4347-480c-8bc5-0b669649599a';
```

### 3. Requirement Not Found

**Error:**
```json
{
  "message": "Requirement with ID 4be657e1-9b65-4ce0-be82-9a9dd2953841 not found",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Solution:** Verify the requirement exists in the database:
```sql
SELECT id, requirement_id, name, storage_url
FROM requirements
WHERE id = '4be657e1-9b65-4ce0-be82-9a9dd2953841';
```

### 4. No File Attached

**Error:**
```json
{
  "message": "Requirement 4be657e1-9b65-4ce0-be82-9a9dd2953841 has no file attached",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** The requirement doesn't have a `storage_url` (file was never uploaded).

**Solution:**
1. Upload a file first using the upload endpoint
2. Verify the requirement has a `storage_url`:
```sql
SELECT id, requirement_id, storage_url
FROM requirements
WHERE id = '4be657e1-9b65-4ce0-be82-9a9dd2953841';
```

### 5. LLM Provider Configuration Error

**Error:**
```json
{
  "message": "Failed to generate embedding: Unsupported LLM provider: undefined",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** Missing or invalid `LLM` environment variable.

**Solution:** Set the LLM provider in your `.env` file:
```bash
LLM=OPENAI  # or GEMINI
OPENAI_API_KEY=your_openai_api_key
```

### 5. Google Cloud Storage Error

**Error:**
```json
{
  "message": "Failed to generate embedding: Failed to download file from URL...",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** Google Cloud Storage configuration issues.

**Solution:** Verify GCS environment variables:
```bash
GOOGLE_CLOUD_BUCKET=your-bucket-name
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_CLIENT_EMAIL=your-service-account-email
GOOGLE_CLOUD_PRIVATE_KEY=your-private-key
```

### 6. Authentication Error

**Error:**
```json
{
  "message": "Unauthorized",
  "statusCode": 401
}
```

**Solution:** 
1. Verify JWT token is valid and not expired
2. Include proper Authorization header:
```bash
-H 'Authorization: Bearer YOUR_VALID_JWT_TOKEN'
```

### 7. OpenAI API Error

**Error:**
```json
{
  "message": "Failed to generate embedding: Request failed with status code 401",
  "error": "Bad Request",
  "statusCode": 400
}
```

**Cause:** Invalid or missing OpenAI API key.

**Solution:** 
1. Verify your OpenAI API key is valid
2. Check your OpenAI account has sufficient credits
3. Update environment variable:
```bash
OPENAI_API_KEY=sk-your-valid-openai-api-key
```

## Debugging Steps

### 1. Check Database Connection

```sql
-- Verify requirement exists
SELECT id, requirement_id, name, storage_url, metadata 
FROM requirements 
WHERE id = 'your-requirement-uuid';

-- Check if embedding already exists
SELECT id, requirement_id, embedding_vector IS NOT NULL as has_embedding
FROM requirements 
WHERE id = 'your-requirement-uuid';
```

### 2. Verify Environment Variables

```bash
# Check if variables are set
echo $LLM
echo $OPENAI_API_KEY
echo $GOOGLE_CLOUD_BUCKET

# Or check in your application
curl -X GET "http://localhost:3002/api/v1/requirements/embedding/models" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Test File Download

```bash
# Check if file exists in Google Cloud Storage
gsutil ls gs://your-bucket-name/requirements/

# Test file download manually
gsutil cp gs://your-bucket-name/path/to/file.pdf ./test-download.pdf
```

### 4. Monitor Server Logs

```bash
# Check application logs for detailed error messages
docker logs your-app-container -f

# Or if running locally
npm run start:dev
```

### 5. Test with Simple File

Create a simple text file to test the process:

1. **Upload a simple text file:**
```bash
curl -X POST "http://localhost:3002/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@simple-test.txt" \
  -F "name=Simple Test Requirement" \
  -F "user_id=your-user-id" \
  -F "company_id=your-company-id" \
  -F "project_id=your-project-id"
```

2. **Get the requirement ID from response**

3. **Test embedding generation:**
```bash
curl -X POST "http://localhost:3002/api/v1/requirements/embedding" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "your-user-id",
    "company_id": "your-company-id",
    "project_id": "your-project-id",
    "id": "requirement-uuid-from-step-2"
  }'
```

## Performance Considerations

### Expected Processing Times

- **Small text files (< 1MB)**: 2-4 seconds
- **PDF documents (1-5MB)**: 3-8 seconds  
- **Large documents (> 5MB)**: 5-15 seconds

### If Processing Takes Too Long

1. **Check file size**: Large files take longer to process
2. **Monitor token usage**: Complex documents use more LLM tokens
3. **Check API rate limits**: OpenAI/Gemini may have rate limiting
4. **Consider async processing**: For large batches, use queue-based processing

## Success Indicators

### Successful Response Example

```json
{
  "vector": [0.1, 0.2, 0.3, ...],  // Array of 1536 numbers
  "dimensions": 1536,
  "model": "text-embedding-ada-002",
  "provider": "openai",
  "tokensUsed": 125,
  "cost": 0.0000375,
  "processingTimeMs": 2500,
  "timestamp": "2024-01-15T10:30:00Z",
  "summary": "This document contains...",
  "fileInfo": {
    "filename": "document.pdf",
    "mimetype": "application/pdf",
    "hasImages": true,
    "hasDiagrams": false,
    "hasComplexLayout": true
  }
}
```

### Database Verification

After successful processing, verify in database:

```sql
-- Check embedding was stored
SELECT 
  id,
  requirement_id,
  embedding_vector IS NOT NULL as has_embedding,
  metadata->>'embeddingStatus' as status,
  metadata->>'llmSummary' as summary_preview
FROM requirements 
WHERE id = 'your-requirement-uuid';

-- Check token usage was tracked
SELECT 
  requirement_id,
  token_type,
  tokens_used,
  cost,
  model,
  provider,
  created_at
FROM token_usage 
WHERE requirement_id = 'your-requirement-uuid'
ORDER BY created_at DESC;
```

## Getting Help

If you continue to experience issues:

1. **Check server logs** for detailed error messages
2. **Verify all environment variables** are properly set
3. **Test with a simple text file** first
4. **Check database connectivity** and table structure
5. **Verify API keys** have sufficient credits/permissions

The embedding endpoint requires multiple services to work together (database, file storage, LLM APIs), so systematic debugging of each component is often necessary.
