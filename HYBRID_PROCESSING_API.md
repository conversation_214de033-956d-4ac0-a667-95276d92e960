# Hybrid File Processing & Vector Embedding API

## Overview

This implementation provides a sophisticated hybrid approach to file processing that combines traditional text extraction with AI-powered validation and vector embeddings for enhanced document understanding.

## Architecture

### Core Components

1. **HybridFileProcessorService** - Orchestrates the hybrid processing workflow
2. **LLMValidationService** - Uses LLM to determine optimal processing method
3. **EmbeddingService** - Generates vector embeddings for semantic search
4. **EmbeddingQueueService** - Manages async embedding generation
5. **TokenUsage** - Tracks AI service usage and costs

### Processing Flow

```
File Upload → Traditional Parse → LLM Validation → Processing Decision
                                                          ↓
                                    Traditional Parse ← → Embedding Process
                                                          ↓
                                                   Vector Storage + Metadata
```

## Database Schema

### New Tables

#### `token_usage`
```sql
- id (UUID, PK)
- requirement_id (UUID, FK)
- token_type (ENUM: embedding, llm_validation, content_extraction, analysis)
- tokens_used (INTEGER)
- model (VARCHAR)
- provider (VARCHAR)
- cost (DECIMAL)
- metadata (JSONB)
- created_at (TIMESTAMP)
```

#### `requirements` (Enhanced)
```sql
-- New columns added:
- embedding_vector (VECTOR(1536)) -- pgvector for similarity search
- metadata (JSONB) -- Processing metadata and status
```

### Metadata Structure
```json
{
  "processingMethod": "traditional_parse|embedding_based|hybrid",
  "embeddingStatus": "not_started|in_progress|completed|failed",
  "embeddingModel": "text-embedding-ada-002",
  "embeddingProvider": "openai",
  "hasImages": true,
  "hasDiagrams": false,
  "hasComplexLayout": true,
  "llmValidationResult": {
    "canUseTraditionalParse": false,
    "confidence": 0.85,
    "reasoning": "Document contains diagrams and complex layout",
    "detectedElements": ["images", "tables", "diagrams"]
  },
  "fileInfo": {
    "originalMimeType": "application/pdf",
    "fileSize": 2048576,
    "pageCount": 15,
    "imageCount": 3
  },
  "processingTimestamp": "2024-01-15T10:30:00Z",
  "embeddingJobId": "embedding_uuid",
  "errorDetails": "Error message if processing failed"
}
```

## API Endpoints

### File Processing (Enhanced Upload)

The existing upload endpoints now automatically use hybrid processing:

```
POST /api/v1/requirements/upload
POST /api/v1/requirements/upload/async
```

**Enhanced Response:**
```json
{
  "id": "requirement-uuid",
  "requirement_id": "R-001",
  "status": "draft",
  "message": "File uploaded and processed",
  "processingMethod": "hybrid",
  "embeddingStatus": "in_progress",
  "embeddingJobId": "embedding_uuid"
}
```

### Embedding Status Tracking

#### Get Embedding Status
```
GET /api/v1/requirements/{id}/embedding/status
```

**Response:**
```json
{
  "requirementId": "requirement-uuid",
  "status": "in_progress",
  "jobId": "embedding_uuid",
  "progress": 75,
  "processingMethod": "embedding_based",
  "embeddingStatus": "in_progress"
}
```

#### Retry Embedding
```
POST /api/v1/requirements/{id}/embedding/retry
```

#### Queue Statistics
```
GET /api/v1/requirements/embeddings/queue/stats
```

## Processing Methods

### 1. Traditional Parse
- **When Used**: Simple text files, high LLM confidence
- **Process**: Standard text extraction (PDF, DOCX, TXT)
- **Speed**: Fast (immediate)
- **Cost**: Low (no AI tokens)

### 2. Embedding-Based
- **When Used**: Complex documents with images/diagrams
- **Process**: LLM validation → Vector embedding generation
- **Speed**: Slower (async processing)
- **Cost**: Higher (LLM + embedding tokens)

### 3. Hybrid
- **When Used**: Documents with mixed content
- **Process**: Traditional parse + embedding for enhanced search
- **Speed**: Medium (basic content immediate, embedding async)
- **Cost**: Medium (embedding tokens only)

## LLM Validation

### Supported Providers
- **OpenAI**: GPT-4 for validation, text-embedding-ada-002 for embeddings
- **Gemini**: Gemini Pro for validation, custom embedding approach

### Validation Criteria
- Image/diagram detection
- Layout complexity analysis
- Text structure assessment
- Content accessibility evaluation

### Example Validation Response
```json
{
  "canUseTraditionalParse": false,
  "confidence": 0.85,
  "reasoning": "Document contains technical diagrams and multi-column layout",
  "detectedElements": ["images", "diagrams", "tables", "complex_layout"],
  "hasImages": true,
  "hasDiagrams": true,
  "hasComplexLayout": true,
  "tokensUsed": 150
}
```

## Vector Search

### Similarity Search
```sql
SELECT * FROM search_similar_requirements(
  query_vector := '[0.1, 0.2, ...]'::vector,
  similarity_threshold := 0.7,
  result_limit := 10
);
```

### Search API (Future Enhancement)
```
POST /api/v1/requirements/search/semantic
{
  "query": "user authentication requirements",
  "limit": 10,
  "threshold": 0.7
}
```

## Token Usage Tracking

### Automatic Tracking
- All LLM and embedding operations are automatically tracked
- Cost calculation based on provider pricing
- Detailed metadata for analysis

### Usage Statistics
```sql
SELECT * FROM get_token_usage_stats(
  start_date := '2024-01-01'::timestamp,
  end_date := '2024-01-31'::timestamp
);
```

### Cost Monitoring
- Real-time cost tracking per operation
- Provider-specific pricing models
- Usage analytics and reporting

## Configuration

### Environment Variables
```env
# LLM Provider
LLM=GEMINI  # or OPENAI
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key

# Embedding Provider
EMBEDDING_PROVIDER=OPENAI  # or GEMINI
```

### Processing Thresholds
- **Confidence Threshold**: 0.8 (for traditional parsing)
- **Similarity Threshold**: 0.7 (for vector search)
- **Max Content Length**: 8000 characters (for embedding)

## Performance Considerations

### Async Processing
- Embedding generation runs in background
- Frontend gets immediate file content
- Progress tracking via WebSocket or polling

### Caching Strategy
- Vector embeddings cached in database
- LLM validation results stored in metadata
- Reuse embeddings for similar content

### Scalability
- Queue-based processing with BullMQ
- Horizontal scaling of workers
- Database indexing for vector operations

## Error Handling

### Fallback Mechanisms
1. **LLM Validation Fails**: Use file type-based heuristics
2. **Embedding Generation Fails**: Continue with traditional content
3. **Vector Storage Fails**: Log error, continue operation

### Retry Logic
- Automatic retry for failed embedding jobs
- Exponential backoff for API failures
- Manual retry endpoints for user intervention

## Monitoring & Analytics

### Queue Monitoring
- Bull Board integration at `/queues`
- Real-time job status and progress
- Failed job analysis and retry

### Usage Analytics
- Token consumption by provider/model
- Cost analysis and optimization
- Processing method effectiveness

### Performance Metrics
- Processing time by file type
- Embedding generation success rate
- Search relevance scoring

## Best Practices

### File Type Handling
- **PDFs**: Always validate with LLM (may contain images)
- **DOCX**: Check for embedded objects
- **Images**: Direct to embedding processing
- **Plain Text**: Use traditional parsing

### Cost Optimization
- Cache validation results for similar files
- Use cheaper models for simple validation
- Batch embedding generation when possible

### Quality Assurance
- Monitor embedding quality with test queries
- Validate search results relevance
- Regular model performance evaluation
