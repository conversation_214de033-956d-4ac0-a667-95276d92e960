const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');
const io = require('socket.io-client');

// Test configuration
const BASE_URL = 'http://localhost:3002';
const TEST_TOKEN = 'test-token'; // Replace with actual JWT token

async function testAsyncUpload() {
  try {
    console.log('🚀 Testing Async Upload API...\n');

    // 1. Create test file
    const testContent = 'This is a test requirement document.\n\nFeatures:\n- Feature 1\n- Feature 2\n- Feature 3';
    fs.writeFileSync('test-requirement.txt', testContent);

    // 2. Upload file asynchronously
    console.log('📤 Uploading file asynchronously...');
    const formData = new FormData();
    formData.append('file', fs.createReadStream('test-requirement.txt'));
    formData.append('name', 'Test Requirement Document');
    formData.append('user_id', 'test-user-123');
    formData.append('company_id', 'test-company-456');
    formData.append('project_id', 'test-project-789');

    const uploadResponse = await fetch(`${BASE_URL}/api/v1/requirements/upload/async`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      },
      body: formData
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log('✅ Upload initiated:', uploadResult);
    
    const jobId = uploadResult.jobId;

    // 3. Connect to WebSocket for real-time updates
    console.log('\n🔌 Connecting to WebSocket...');
    const socket = io(`${BASE_URL}/upload-progress`);

    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      
      // Subscribe to job updates
      socket.emit('subscribe-to-job', { jobId });
      console.log(`📡 Subscribed to job: ${jobId}`);
    });

    socket.on('job-status-update', (data) => {
      console.log(`📊 Progress: ${data.progress.percentage}% - ${data.progress.description}`);
    });

    socket.on('job-completed', (data) => {
      console.log('🎉 Job completed!', data.result);
      socket.disconnect();
      cleanup();
    });

    socket.on('job-failed', (data) => {
      console.log('❌ Job failed:', data.error);
      socket.disconnect();
      cleanup();
    });

    socket.on('error', (error) => {
      console.log('🔥 WebSocket error:', error);
    });

    // 4. Poll job status via REST API
    console.log('\n🔄 Polling job status...');
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await fetch(`${BASE_URL}/api/v1/requirements/jobs/${jobId}/status`, {
          headers: {
            'Authorization': `Bearer ${TEST_TOKEN}`
          }
        });

        if (statusResponse.ok) {
          const status = await statusResponse.json();
          console.log(`📋 REST Status: ${status.status} (${status.progress.percentage}%)`);
          
          if (status.status === 'completed' || status.status === 'failed') {
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.log('⚠️ Status poll error:', error.message);
      }
    }, 2000);

    // 5. Test queue stats
    setTimeout(async () => {
      try {
        const statsResponse = await fetch(`${BASE_URL}/api/v1/requirements/queue/stats`, {
          headers: {
            'Authorization': `Bearer ${TEST_TOKEN}`
          }
        });

        if (statsResponse.ok) {
          const stats = await statsResponse.json();
          console.log('\n📈 Queue Stats:', stats);
        }
      } catch (error) {
        console.log('⚠️ Stats error:', error.message);
      }
    }, 1000);

    // Cleanup after 30 seconds
    setTimeout(() => {
      console.log('\n⏰ Test timeout - cleaning up...');
      socket.disconnect();
      clearInterval(pollInterval);
      cleanup();
    }, 30000);

  } catch (error) {
    console.error('❌ Test failed:', error);
    cleanup();
  }
}

function cleanup() {
  try {
    if (fs.existsSync('test-requirement.txt')) {
      fs.unlinkSync('test-requirement.txt');
      console.log('🧹 Cleaned up test file');
    }
  } catch (error) {
    console.log('⚠️ Cleanup error:', error.message);
  }
  
  console.log('\n✨ Test completed!');
  process.exit(0);
}

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Run the test
testAsyncUpload();
