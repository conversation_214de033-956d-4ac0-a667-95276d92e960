# Testing the Updated Embedding Endpoint

## Quick Test Guide

### Prerequisites

1. **Start the server**:
   ```bash
   npm run start:dev
   ```

2. **Configure environment variables**:
   ```bash
   # LLM Provider (uses .env file)
   LLM=OPENAI  # or GEMINI
   OPENAI_API_KEY=your_openai_api_key
   GEMINI_API_KEY=your_gemini_api_key

   # Google Cloud Storage
   GOOGLE_CLOUD_BUCKET=your-bucket-name
   GOOGLE_CLOUD_PROJECT_ID=your-project-id
   GOOGLE_CLOUD_CLIENT_EMAIL=your-service-account-email
   GOOGLE_CLOUD_PRIVATE_KEY=your-private-key
   ```

3. **Get JWT token**:
   ```bash
   # Login to get token
   curl -X POST "http://localhost:3000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"email": "your-email", "password": "your-password"}'
   ```

4. **Set test variables**:
   ```bash
   export JWT_TOKEN="your-jwt-token-here"
   export BASE_URL="http://localhost:3000"
   ```

### Test 1: Upload a Requirement File First

Before testing embedding, you need a requirement with a file:

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -F "file=@test_document.pdf" \
  -F "name=Test Authentication Requirements" \
  -F "user_id=user-uuid-123" \
  -F "company_id=company-uuid-456" \
  -F "project_id=project-uuid-789"
```

**Save the requirement ID from the response for the next test.**

### Test 2: Generate Embedding from File

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6",
    "company_id": "704f4925-9bd3-46b4-8bce-28f8671ef482",
    "project_id": "f907b2b1-4347-480c-8bc5-0b669649599a",
    "id": "4be657e1-9b65-4ce0-be82-9a9dd2953841"
  }' | jq '.'
```

**Expected Response**:
```json
{
  "vector": [0.1, 0.2, 0.3, ...], // 1536 numbers
  "dimensions": 1536,
  "model": "text-embedding-ada-002",
  "provider": "openai",
  "tokensUsed": 125,
  "cost": 0.0000375,
  "processingTimeMs": 2500,
  "timestamp": "2024-01-15T10:30:00Z",
  "summary": "This document contains user authentication requirements...",
  "fileInfo": {
    "filename": "test_document.pdf",
    "mimetype": "application/pdf",
    "hasImages": true,
    "hasDiagrams": false,
    "hasComplexLayout": true
  }
}
```

### Test 3: Semantic Search

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding/search" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "user login and authentication",
    "limit": 5,
    "threshold": 0.7
  }' | jq '.'
```

**Expected Response**:
```json
[
  {
    "requirement": {
      "id": "req-uuid-1",
      "requirementId": "R-001",
      "name": "User Authentication",
      "content": "The system shall authenticate users...",
      "metadata": {}
    },
    "similarity": 0.85
  }
]
```

### Test 4: Get Available Models

```bash
curl -X GET "${BASE_URL}/api/v1/requirements/embedding/models" \
  -H "Authorization: Bearer ${JWT_TOKEN}" | jq '.'
```

### Test 5: Test Different Providers

#### OpenAI Provider
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Security requirements for data encryption",
    "provider": "openai",
    "model": "text-embedding-ada-002"
  }' | jq '.provider, .model, .dimensions'
```

#### Gemini Provider (if configured)
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Security requirements for data encryption",
    "provider": "gemini",
    "model": "models/embedding-001"
  }' | jq '.provider, .model, .dimensions'
```

## Advanced Testing

### Test 6: Large Text Content

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "The system shall implement a comprehensive user authentication mechanism that supports multiple authentication methods including username/password, OAuth 2.0, SAML, and multi-factor authentication. The authentication system must be secure, scalable, and provide seamless user experience across all platforms including web, mobile, and desktop applications. Security requirements include password complexity rules, account lockout policies, session management, and audit logging of all authentication events."
  }' | jq '.tokensUsed, .cost, .processingTimeMs'
```

### Test 7: Error Handling

#### Test with text too long
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "'$(python3 -c "print('A' * 9000)")"'"
  }'
```

#### Test without authentication
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Test without auth"
  }'
```

### Test 8: Performance Testing

```bash
# Test multiple requests in parallel
for i in {1..5}; do
  curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
    -H "Authorization: Bearer ${JWT_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
      \"text\": \"Performance test request number ${i} with unique content\",
      \"context\": \"Performance testing batch ${i}\"
    }" &
done
wait
```

## Validation Checklist

### ✅ **Basic Functionality**
- [ ] Embedding generation works
- [ ] Vector has correct dimensions (1536 for OpenAI, 768 for Gemini)
- [ ] Token usage is calculated
- [ ] Cost is computed correctly
- [ ] Processing time is reasonable (<500ms)

### ✅ **Authentication & Security**
- [ ] JWT token is required
- [ ] Invalid tokens are rejected
- [ ] Input validation works (text length limits)
- [ ] SQL injection protection

### ✅ **Provider Support**
- [ ] OpenAI provider works (if API key configured)
- [ ] Gemini provider works (if API key configured)
- [ ] Fallback behavior on provider failure
- [ ] Model selection works

### ✅ **Search Functionality**
- [ ] Semantic search returns relevant results
- [ ] Similarity scores are reasonable (0-1 range)
- [ ] Threshold filtering works
- [ ] Limit parameter works

### ✅ **Error Handling**
- [ ] Graceful handling of API failures
- [ ] Proper error messages
- [ ] HTTP status codes are correct
- [ ] No sensitive data in error responses

### ✅ **Performance**
- [ ] Response times are acceptable
- [ ] Concurrent requests work
- [ ] Memory usage is stable
- [ ] No memory leaks

## Database Verification

### Check Token Usage Tracking

```sql
-- Check if token usage is being tracked
SELECT 
  requirement_id,
  token_type,
  tokens_used,
  model,
  provider,
  cost,
  created_at
FROM token_usage 
WHERE requirement_id = 'req-test-123'
ORDER BY created_at DESC;
```

### Check Vector Storage

```sql
-- Check if embeddings are stored properly
SELECT 
  id,
  requirement_id,
  name,
  CASE 
    WHEN embedding_vector IS NOT NULL THEN 'Has Vector'
    ELSE 'No Vector'
  END as vector_status,
  metadata->>'embeddingStatus' as embedding_status
FROM requirements 
WHERE id = 'your-requirement-id';
```

## Troubleshooting

### Common Issues

1. **"Failed to generate embedding"**
   - Check API keys in environment variables
   - Verify provider service is available
   - Check network connectivity

2. **"Unauthorized"**
   - Verify JWT token is valid and not expired
   - Check token format (Bearer prefix)
   - Ensure user has proper permissions

3. **"Text content cannot exceed 8000 characters"**
   - Reduce input text length
   - Split large content into chunks
   - Use summarization before embedding

4. **Empty search results**
   - Check if requirements have embeddings
   - Lower similarity threshold
   - Verify vector data exists in database

### Debug Commands

```bash
# Check server logs
docker logs your-app-container -f

# Check database connection
psql -h localhost -U your_user -d requirement_service -c "SELECT COUNT(*) FROM requirements;"

# Check Redis connection (for queues)
redis-cli ping

# Check environment variables
env | grep -E "(OPENAI|GEMINI|EMBEDDING)"
```

## Next Steps

After successful testing:

1. **Integrate with frontend** - Add embedding search to your UI
2. **Implement caching** - Cache frequently used embeddings
3. **Add batch processing** - Process multiple texts at once
4. **Monitor costs** - Set up alerts for high token usage
5. **Optimize performance** - Tune vector indexes and queries

The embedding endpoint is now ready for production use with comprehensive vector search capabilities! 🚀
