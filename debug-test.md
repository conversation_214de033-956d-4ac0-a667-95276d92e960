# Debug Steps for Requirements API

## Issue Analysis

The error you're experiencing has two parts:

### 1. Validation Error (400 Bad Request)
```
"message": [
    "page must be a number string",
    "limit must be a number string"
]
```

**Fixed**: Updated the DTO to use `@Type(() => Number)` instead of `@Transform()` for proper number conversion.

### 2. Database Connection Error
```
Cannot read properties of undefined (reading 'databaseName')
```

**Likely Cause**: The database schema changes require the application to be restarted, and the old requirement_statuses table structure is causing issues.

## Solution Steps

### Step 1: Stop the Application
```bash
# Stop your current NestJS application (Ctrl+C)
```

### Step 2: Update Database Schema
Run the migration script to update your database:

```bash
psql -d requirement_service -f migration-to-new-schema.sql
```

Or manually run this SQL:
```sql
-- Drop and recreate requirement_statuses table
DROP TABLE IF EXISTS requirement_statuses CASCADE;

CREATE TABLE requirement_statuses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT
);

-- Insert status data
INSERT INTO requirement_statuses (name, description) VALUES
('draft', 'The requirement has been uploaded but not yet processed.'),
('reviewed_by_ai', 'AI analysis is complete and awaiting user review.'),
('revised', 'The requirement has been modified by the user after AI review.'),
('approved', 'The user has approved the requirement.'),
('published', 'The requirement is finalized and ready for use.'),
('rejected', 'The user has rejected the requirement.');

-- Update requirements table
ALTER TABLE requirements ADD COLUMN IF NOT EXISTS storage_url VARCHAR(500);

-- Fix status_id column if needed
ALTER TABLE requirements DROP CONSTRAINT IF EXISTS "FK_e2d32dc1aa552fa8721736894ba";
ALTER TABLE requirements ADD COLUMN IF NOT EXISTS status_id_new INTEGER;

UPDATE requirements SET status_id_new = (
    SELECT id FROM requirement_statuses WHERE name = 'draft'
) WHERE status_id = 'draft' OR status_id IS NULL;

-- If you have other statuses, update them accordingly
-- UPDATE requirements SET status_id_new = (SELECT id FROM requirement_statuses WHERE name = 'reviewed_by_ai') WHERE status_id = 'reviewed_by_ai';

-- Drop old column and rename new one
ALTER TABLE requirements DROP COLUMN IF EXISTS status_id;
ALTER TABLE requirements RENAME COLUMN status_id_new TO status_id;
ALTER TABLE requirements ALTER COLUMN status_id SET NOT NULL;

-- Add foreign key constraint
ALTER TABLE requirements 
ADD CONSTRAINT FK_requirements_status_id 
FOREIGN KEY (status_id) REFERENCES requirement_statuses(id);
```

### Step 3: Restart Application
```bash
npm run start:dev
```

### Step 4: Test the API
```bash
curl -X GET "http://localhost:3002/api/v1/requirements?project_id=f907b2b1-4347-480c-8bc5-0b669649599a&page=1&limit=50" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Expected Result

After these steps, you should see:
1. No validation errors for page/limit parameters
2. Successful database queries
3. Requirements list with storage_url field included

## If Issues Persist

1. Check database connection in logs
2. Verify environment variables are set correctly
3. Ensure requirement_statuses table has data:
   ```sql
   SELECT * FROM requirement_statuses;
   ```
4. Check if requirements table has the new schema:
   ```sql
   \d requirements
   ```
