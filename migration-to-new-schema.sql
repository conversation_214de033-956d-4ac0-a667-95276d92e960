-- Migration script to update existing database to new schema
-- Run this if you already have data in the old schema

-- Step 1: Backup existing data (optional but recommended)
-- CREATE TABLE requirements_backup AS SELECT * FROM requirements;
-- CREATE TABLE requirement_statuses_backup AS SELECT * FROM requirement_statuses;

-- Step 2: Drop foreign key constraints
ALTER TABLE requirements DROP CONSTRAINT IF EXISTS "FK_e2d32dc1aa552fa8721736894ba";

-- Step 3: Create new requirement_statuses table with auto-increment ID
DROP TABLE IF EXISTS requirement_statuses CASCADE;

CREATE TABLE requirement_statuses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT
);

-- Step 4: Insert status data
INSERT INTO requirement_statuses (name, description) VALUES
('draft', 'The requirement has been uploaded but not yet processed.'),
('reviewed_by_ai', 'AI analysis is complete and awaiting user review.'),
('revised', 'The requirement has been modified by the user after AI review.'),
('approved', 'The user has approved the requirement.'),
('published', 'The requirement is finalized and ready for use.'),
('rejected', 'The user has rejected the requirement.');

-- Step 5: Add storage_url column to requirements table
ALTER TABLE requirements ADD COLUMN IF NOT EXISTS storage_url VARCHAR(500);

-- Step 6: Update status_id column type and data
-- First, add a temporary column
ALTER TABLE requirements ADD COLUMN status_id_new INTEGER;

-- Update the new column based on old values
UPDATE requirements SET status_id_new = (
    CASE 
        WHEN status_id = 'draft' THEN (SELECT id FROM requirement_statuses WHERE name = 'draft')
        WHEN status_id = 'reviewed_by_ai' THEN (SELECT id FROM requirement_statuses WHERE name = 'reviewed_by_ai')
        WHEN status_id = 'revised' THEN (SELECT id FROM requirement_statuses WHERE name = 'revised')
        WHEN status_id = 'approved' THEN (SELECT id FROM requirement_statuses WHERE name = 'approved')
        WHEN status_id = 'published' THEN (SELECT id FROM requirement_statuses WHERE name = 'published')
        WHEN status_id = 'rejected' THEN (SELECT id FROM requirement_statuses WHERE name = 'rejected')
        ELSE (SELECT id FROM requirement_statuses WHERE name = 'draft') -- Default to draft
    END
);

-- Drop old column and rename new one
ALTER TABLE requirements DROP COLUMN status_id;
ALTER TABLE requirements RENAME COLUMN status_id_new TO status_id;

-- Step 7: Add foreign key constraint
ALTER TABLE requirements 
ADD CONSTRAINT FK_requirements_status_id 
FOREIGN KEY (status_id) REFERENCES requirement_statuses(id);

-- Step 8: Update indexes
DROP INDEX IF EXISTS idx_requirements_status;
CREATE INDEX idx_requirements_status ON requirements(status_id);

-- Step 9: Add NOT NULL constraint to status_id
ALTER TABLE requirements ALTER COLUMN status_id SET NOT NULL;

COMMIT;
