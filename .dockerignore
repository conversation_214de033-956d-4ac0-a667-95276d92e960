# Node modules (will be installed in Docker)
node_modules

# Build output
dist

# Logs
logs
*.log

# Test and coverage
test
coverage

# Editor directories and OS files
.vscode
.DS_Store

# Environment files (uncomment if you use them and don't want them in the image)
# .env

# NPM/Yarn/PNPM lock files (optional, usually needed for reproducible builds)
# yarn.lock
# pnpm-lock.yaml

# Misc
npm-debug.log
