import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Requirement } from './requirement.entity';

export enum TokenType {
  EMBEDDING = 'embedding',
  LLM_VALIDATION = 'llm_validation',
  CONTENT_EXTRACTION = 'content_extraction',
  ANALYSIS = 'analysis'
}

@Entity('token_usage')
export class TokenUsage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'requirement_id' })
  requirementId: string;

  @Column({
    type: 'enum',
    enum: TokenType,
    name: 'token_type'
  })
  tokenType: TokenType;

  @Column({ type: 'int', name: 'tokens_used' })
  tokensUsed: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  model: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  provider: string; // 'openai', 'gemini', etc.

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  cost: number; // Cost in USD

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional context like prompt type, response length, etc.

  @CreateDateColumn({ name: 'created_at' })
  timestamp: Date;

  // Relations
  @ManyToOne(() => Requirement, requirement => requirement.tokenUsages)
  @JoinColumn({ name: 'requirement_id' })
  requirement: Requirement;
}
