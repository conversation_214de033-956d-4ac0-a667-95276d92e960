import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Requirement } from './requirement.entity';

@Entity('ai_analyses')
export class AiAnalysis {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'requirement_id' })
  requirementId: string;

  @CreateDateColumn({ name: 'analysis_date' })
  analysisDate: Date;

  @Column({ type: 'int', name: 'ai_quality_score', nullable: true })
  aiQualityScore: number;

  @Column({ type: 'jsonb', name: 'ai_feedback', nullable: true })
  aiFeedback: any;

  // Relations
  @ManyToOne(() => Requirement, requirement => requirement.analyses, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requirement_id' })
  requirement: Requirement;
}
