import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { Requirement } from './requirement.entity';
import { User } from './user.entity';

@Entity('requirement_revisions')
@Unique(['requirementId', 'revisionNumber'])
export class RequirementRevision {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'requirement_id' })
  requirementId: string;

  @Column({ type: 'int', name: 'revision_number' })
  revisionNumber: number;

  @Column({ type: 'text', name: 'content_change', nullable: true })
  contentChange: string;

  @Column({ type: 'bigint', name: 'modified_by', nullable: true })
  modifiedById: number;

  @CreateDateColumn({ name: 'modified_at' })
  modifiedAt: Date;

  // Relations
  @ManyToOne(() => Requirement, requirement => requirement.revisions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requirement_id' })
  requirement: Requirement;

  @ManyToOne(() => User, user => user.revisions)
  @JoinColumn({ name: 'modified_by' })
  modifiedBy: User;
}
