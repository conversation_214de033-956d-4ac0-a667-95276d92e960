import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Requirement } from './requirement.entity';
import { RequirementRevision } from './requirement-revision.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ type: 'uuid', name: 'user_id' })
  userId: string;

  @Column({ type: 'uuid', name: 'company_id' })
  companyId: string;

  @Column({ type: 'uuid', name: 'project_id' })
  projectId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => Requirement, requirement => requirement.uploadedBy)
  requirements: Requirement[];

  @OneToMany(() => RequirementRevision, revision => revision.modifiedBy)
  revisions: RequirementRevision[];
}
