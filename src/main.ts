import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS
  app.enableCors({
    origin: ['http://localhost:5174', 'http://localhost:5173', 'https://app.agentq.id', 'https://staging-app.agentq.id'],
    methods: ['GET', 'POST', 'PATCH', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true,
    allowedHeaders: 'Content-Type,Authorization',
  });
  
  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Get Bull Board instance
  const bullBoard = app.get('BULL_BOARD');
  app.use('/queues', bullBoard.serverAdapter.getRouter());

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Requirement Service API')
    .setDescription(`
      API documentation for the AI Service.
      
      ## Supported File Types
      - Word Documents (.doc, .docx)
      - Markdown (.md)
      - Text Files (.txt)
      - PDF Documents (.pdf)
      - HTML Files (.html)
      - Excel Spreadsheets (.xls, .xlsx)
      - CSV Files (.csv)
      
      ## Authentication
      This service uses JWT authentication shared with the main backend service.
      Include the JWT token in the Authorization header:
      \`\`\`
      Authorization: Bearer <your_jwt_token>
      \`\`\`
    `)
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('file-upload', 'File upload operations')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);
  
  await app.listen(3002);
  console.log('Application is running on: http://localhost:3002');
  console.log('Swagger documentation is available at: http://localhost:3002/api');
  console.log('Bull Board is available at: http://localhost:3002/queues');
}
bootstrap();