import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class GoogleCloudStorageService {
  private readonly logger = new Logger(GoogleCloudStorageService.name);
  private storage: Storage;
  private bucketName: string;

  constructor(private configService: ConfigService) {
    this.bucketName = this.configService.get<string>('GOOGLE_CLOUD_BUCKET');
    
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID'),
      credentials: {
        client_email: this.configService.get<string>('GOOGLE_CLOUD_CLIENT_EMAIL'),
        private_key: this.configService.get<string>('GOOGLE_CLOUD_PRIVATE_KEY')?.replace(/\\n/g, '\n'),
      },
    });

    this.logger.log(`Initialized Google Cloud Storage with bucket: ${this.bucketName}`);
  }

  async uploadRequirementFile(
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
    agentqId: string,
  ): Promise<string> {
    try {
      // Generate unique filename
      const fileExtension = this.getFileExtension(originalFilename);
      const uniqueFilename = `${agentqId}_${uuidv4()}${fileExtension}`;
      const filePath = `requirements/${uniqueFilename}`;

      // Get bucket reference
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(filePath);

      // Create write stream
      const stream = file.createWriteStream({
        metadata: {
          contentType: mimetype,
          metadata: {
            originalName: originalFilename,
            agentqId: agentqId,
            uploadedAt: new Date().toISOString(),
          },
        },
        resumable: false,
      });

      return new Promise((resolve, reject) => {
        stream.on('error', (error) => {
          this.logger.error(`Failed to upload file ${originalFilename}:`, error);
          reject(error);
        });

        stream.on('finish', async () => {
          try {
            const publicUrl = `https://storage.googleapis.com/${this.bucketName}/${filePath}`;
            this.logger.log(`Successfully uploaded file: ${publicUrl}`);
            resolve(publicUrl);
          } catch (error) {
            this.logger.error(`Error after upload:`, error);
            reject(error);
          }
        });

        stream.end(fileBuffer);
      });
    } catch (error) {
      this.logger.error(`Error uploading file to Google Cloud Storage:`, error);
      throw error;
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(filePath);

      // Check if file exists first
      const [exists] = await file.exists();
      if (!exists) {
        this.logger.warn(`File does not exist, skipping deletion: ${filePath}`);
        return; // Don't throw error, just log warning
      }

      await file.delete();
      this.logger.log(`Successfully deleted file: ${filePath}`);
    } catch (error) {
      // Check if error is "file not found" and handle gracefully
      if (error.message && error.message.includes('No such object')) {
        this.logger.warn(`File not found during deletion (already deleted?): ${filePath}`);
        return; // Don't throw error for missing files
      }

      this.logger.error(`Failed to delete file ${filePath}:`, error);
      throw error;
    }
  }

  async deleteRequirementFile(storageUrl: string): Promise<void> {
    try {
      // Extract file path from storage URL
      // URL format: https://storage.googleapis.com/{bucket}/{filePath}
      const urlParts = storageUrl.split(`https://storage.googleapis.com/${this.bucketName}/`);
      if (urlParts.length !== 2) {
        throw new Error(`Invalid storage URL format: ${storageUrl}`);
      }

      const filePath = urlParts[1];
      await this.deleteFile(filePath);
      this.logger.log(`Successfully deleted requirement file from URL: ${storageUrl}`);
    } catch (error) {
      // Check if error is "file not found" and handle gracefully
      if (error.message && error.message.includes('No such object')) {
        this.logger.warn(`Requirement file not found during deletion (already deleted?): ${storageUrl}`);
        return; // Don't throw error for missing files during bulk delete
      }

      this.logger.error(`Failed to delete requirement file from URL ${storageUrl}:`, error);
      throw error;
    }
  }

  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  async getSignedUrl(filePath: string, expiresInMinutes: number = 60): Promise<string> {
    try {
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(filePath);

      const [signedUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + expiresInMinutes * 60 * 1000,
      });

      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${filePath}:`, error);
      throw error;
    }
  }

  async downloadFile(storageUrl: string): Promise<Buffer> {
    try {
      // Extract file path from storage URL
      // URL format: https://storage.googleapis.com/{bucket}/{filePath}
      const urlParts = storageUrl.split(`https://storage.googleapis.com/${this.bucketName}/`);
      if (urlParts.length !== 2) {
        throw new Error(`Invalid storage URL format: ${storageUrl}`);
      }

      const filePath = urlParts[1];
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(filePath);

      // Download file as buffer
      const [fileBuffer] = await file.download();

      this.logger.log(`Successfully downloaded file: ${filePath}`);
      return fileBuffer;
    } catch (error) {
      this.logger.error(`Failed to download file from URL ${storageUrl}:`, error);
      throw error;
    }
  }
}