import { DataSource } from 'typeorm';
import { RequirementStatus } from '../../entities/requirement-status.entity';

export async function seedRequirementStatuses(dataSource: DataSource): Promise<void> {
  const repository = dataSource.getRepository(RequirementStatus);

  const statuses = [
    {
      name: 'draft',
      description: 'The requirement has been uploaded but not yet processed.',
    },
    {
      name: 'reviewed_by_ai',
      description: 'AI analysis is complete and awaiting user review.',
    },
    {
      name: 'revised',
      description: 'The requirement has been modified by the user after AI review.',
    },
    {
      name: 'approved',
      description: 'The user has approved the requirement.',
    },
    {
      name: 'published',
      description: 'The requirement is finalized and ready for use.',
    },
    {
      name: 'rejected',
      description: 'The user has rejected the requirement.',
    },
  ];

  for (const status of statuses) {
    const existingStatus = await repository.findOne({ where: { name: status.name } });
    if (!existingStatus) {
      await repository.save(repository.create(status));
      console.log(`Created requirement status: ${status.name}`);
    }
  }
}
