import { ValueTransformer } from 'typeorm';

/**
 * Custom TypeORM transformer for PostgreSQL vector type (pgvector extension)
 * Handles conversion between JavaScript number arrays and PostgreSQL vector format
 */
export class VectorTransformer implements ValueTransformer {
  /**
   * Transform from database value to JavaScript value
   * @param value - Raw database value (string representation of vector)
   * @returns number array or null
   */
  from(value: any): number[] | null {
    if (!value) return null;
    
    try {
      // Handle different possible formats from database
      if (typeof value === 'string') {
        // Remove brackets and split by comma
        const cleanValue = value.replace(/[\[\]]/g, '');
        return cleanValue.split(',').map(num => parseFloat(num.trim()));
      }
      
      if (Array.isArray(value)) {
        return value.map(num => parseFloat(num));
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing vector from database:', error);
      return null;
    }
  }

  /**
   * Transform from JavaScript value to database value
   * @param value - JavaScript number array
   * @returns string representation for PostgreSQL vector
   */
  to(value: any): string | null {
    if (!value || !Array.isArray(value)) return null;
    
    try {
      // Format as PostgreSQL vector literal: [1.0,2.0,3.0]
      return `[${value.join(',')}]`;
    } catch (error) {
      console.error('Error formatting vector for database:', error);
      return null;
    }
  }
}

/**
 * Helper function to create vector column options
 * @param dimensions - Number of dimensions for the vector
 * @returns Column options for TypeORM
 */
export const createVectorColumn = (dimensions: number = 1536) => ({
  type: 'text' as any,
  transformer: new VectorTransformer(),
  nullable: true,
  comment: `Vector embedding with ${dimensions} dimensions for semantic search`
});
