import { DataSource } from 'typeorm';
import { seedRequirementStatuses } from './seeds/requirement-statuses.seed';
import * as entities from '../entities';

async function initializeDatabase() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || '127.0.0.1',
    port: parseInt(process.env.DB_PORT ?? '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'requirement_service',
    entities: [__dirname + '/**/*.entity{.ts,.js}'],
    synchronize: process.env.NODE_ENV !== 'production',
    logging: true, // Add this to see SQL queries
  });

  try {
    console.log('Attempting to connect to database...');
    await dataSource.initialize();
    console.log('Database connection established');

    await dataSource.destroy();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  initializeDatabase();
}
