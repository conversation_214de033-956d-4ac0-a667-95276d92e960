-- Migration: Add context column to requirements table
-- Date: 2024-01-15
-- Description: Adds context column for enhanced requirement content (original + LLM insights)

-- Add context column to requirements table
ALTER TABLE requirements ADD COLUMN IF NOT EXISTS context TEXT;

-- Add index for context search
CREATE INDEX IF NOT EXISTS idx_requirements_context ON requirements USING GIN (to_tsvector('english', context));

-- Add comment for documentation
COMMENT ON COLUMN requirements.context IS 'Enhanced requirement content combining original text with LLM-extracted insights from images, diagrams, and complex layouts';

-- Update existing requirements to copy content to context (optional)
-- UPDATE requirements SET context = content WHERE context IS NULL;
