-- Migration: Add vector embedding and token usage functionality
-- Date: 2024-01-15
-- Description: Adds vector column to requirements table and creates token_usage table

-- Enable pgvector extension (run as superuser if needed)
CREATE EXTENSION IF NOT EXISTS vector;

-- Create token_usage table
CREATE TABLE IF NOT EXISTS token_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL,
    token_type VARCHAR(50) NOT NULL CHECK (token_type IN ('embedding', 'llm_validation', 'content_extraction', 'analysis')),
    tokens_used INTEGER NOT NULL,
    model VARCHAR(50),
    provider VARCHAR(100),
    cost DECIMAL(10, 6),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT fk_token_usage_requirement 
        FOREIGN KEY (requirement_id) 
        REFERENCES requirements(id) 
        ON DELETE CASCADE
);

-- Add indexes for token_usage
CREATE INDEX IF NOT EXISTS idx_token_usage_requirement_id ON token_usage(requirement_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_token_type ON token_usage(token_type);
CREATE INDEX IF NOT EXISTS idx_token_usage_created_at ON token_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_token_usage_provider ON token_usage(provider);

-- Add vector and metadata columns to requirements table
-- Note: Adjust vector dimension based on your embedding model
-- OpenAI text-embedding-ada-002: 1536 dimensions
-- OpenAI text-embedding-3-small: 1536 dimensions
-- OpenAI text-embedding-3-large: 3072 dimensions
-- Gemini embedding models: typically 768 dimensions

-- First, add the column as vector type (this is the actual PostgreSQL type)
ALTER TABLE requirements
ADD COLUMN IF NOT EXISTS embedding_vector vector(1536),
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Note: TypeORM will see this as 'text' type due to its limitations with custom types,
-- but PostgreSQL will handle it as proper vector type for operations

-- Add indexes for vector similarity search
CREATE INDEX IF NOT EXISTS idx_requirements_embedding_vector 
ON requirements USING ivfflat (embedding_vector vector_cosine_ops) 
WITH (lists = 100);

-- Add index for metadata queries
CREATE INDEX IF NOT EXISTS idx_requirements_metadata ON requirements USING GIN (metadata);

-- Add specific indexes for common metadata queries
CREATE INDEX IF NOT EXISTS idx_requirements_embedding_status 
ON requirements USING BTREE ((metadata->>'embeddingStatus'));

CREATE INDEX IF NOT EXISTS idx_requirements_processing_method 
ON requirements USING BTREE ((metadata->>'processingMethod'));

-- Create a function to search similar requirements
CREATE OR REPLACE FUNCTION search_similar_requirements(
    query_vector vector(1536),
    similarity_threshold FLOAT DEFAULT 0.7,
    result_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    requirement_id VARCHAR(50),
    name VARCHAR(255),
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id,
        r.requirement_id,
        r.name,
        1 - (r.embedding_vector <=> query_vector) as similarity
    FROM requirements r
    WHERE r.embedding_vector IS NOT NULL
      AND 1 - (r.embedding_vector <=> query_vector) > similarity_threshold
    ORDER BY r.embedding_vector <=> query_vector
    LIMIT result_limit;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get token usage statistics
CREATE OR REPLACE FUNCTION get_token_usage_stats(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    token_type VARCHAR(50),
    provider VARCHAR(100),
    total_tokens BIGINT,
    total_cost DECIMAL(10, 6),
    usage_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tu.token_type,
        tu.provider,
        SUM(tu.tokens_used) as total_tokens,
        SUM(tu.cost) as total_cost,
        COUNT(*) as usage_count
    FROM token_usage tu
    WHERE tu.created_at >= start_date 
      AND tu.created_at <= end_date
    GROUP BY tu.token_type, tu.provider
    ORDER BY total_tokens DESC;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE token_usage IS 'Tracks token usage for LLM and embedding operations';
COMMENT ON COLUMN requirements.embedding_vector IS 'Vector embedding for semantic search (1536 dimensions for OpenAI ada-002)';
COMMENT ON COLUMN requirements.metadata IS 'JSON metadata including processing method, embedding status, and file information';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON token_usage TO your_app_user;
-- GRANT USAGE ON SEQUENCE token_usage_id_seq TO your_app_user;
