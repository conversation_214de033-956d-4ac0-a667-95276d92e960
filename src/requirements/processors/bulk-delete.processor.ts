import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Job } from 'bullmq';
import { Requirement, TokenUsage, AiAnalysis, RequirementRevision } from '../../entities';
import { GoogleCloudStorageService } from '../../storage/google-cloud-storage.service';
import { BulkDeleteJobData, BulkDeleteJobResult, BulkDeleteProgressUpdate } from '../interfaces/bulk-delete-job.interface';

@Injectable()
@Processor('bulk-delete-queue')
export class BulkDeleteProcessor extends WorkerHost {
  private readonly logger = new Logger(BulkDeleteProcessor.name);

  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
    @InjectRepository(AiAnalysis)
    private aiAnalysisRepository: Repository<AiAnalysis>,
    @InjectRepository(RequirementRevision)
    private requirementRevisionRepository: Repository<RequirementRevision>,
    private googleCloudStorageService: GoogleCloudStorageService,
    @Inject(forwardRef(() => 'UploadProgressGateway'))
    private uploadProgressGateway: any, // Use any to avoid circular dependency issues
  ) {
    super();
  }

  async process(job: Job<BulkDeleteJobData>): Promise<BulkDeleteJobResult> {
    const { requirementIds, userId, reason, deleteFiles, jobId } = job.data;
    
    this.logger.log(`Starting bulk delete job ${jobId} for ${requirementIds.length} requirements`);

    try {
      // Step 1: Validate requirements exist (10%)
      await this.updateProgress(job, 'validating', 10, 'Validating requirements...', 0, requirementIds.length);
      
      const requirements = await this.requirementRepository.find({
        where: { id: In(requirementIds) },
        relations: ['status']
      });

      const foundIds = requirements.map(req => req.id);
      const notFoundIds = requirementIds.filter(id => !foundIds.includes(id));

      if (notFoundIds.length > 0) {
        this.logger.warn(`Some requirements not found: ${notFoundIds.join(', ')}`);
      }

      // Step 2: Delete files from storage if requested (20-70%)
      const deletedRequirements: { id: string; requirementId: string; name: string }[] = [];
      const failedRequirements: { id: string; requirementId: string; name: string; error: string }[] = [];
      
      let processedCount = 0;

      for (const requirement of requirements) {
        try {
          // Update progress
          processedCount++;
          const progressPercentage = 20 + Math.floor((processedCount / requirements.length) * 50);
          await this.updateProgress(
            job, 
            'deleting_files', 
            progressPercentage, 
            `Deleting files and data (${processedCount}/${requirements.length})...`,
            processedCount,
            requirements.length
          );

          // Delete file from storage if requested and URL exists
          if (deleteFiles && requirement.storageUrl) {
            try {
              await this.googleCloudStorageService.deleteRequirementFile(requirement.storageUrl);
              this.logger.log(`Deleted file for requirement ${requirement.requirementId}`);
            } catch (fileError) {
              this.logger.warn(`Failed to delete file for requirement ${requirement.requirementId}:`, fileError);
              // Continue with database deletion even if file deletion fails
            }
          }

          // Delete related records first to avoid foreign key constraint violations
          await this.deleteRelatedRecords(requirement.id);

          // Delete from database
          await this.requirementRepository.remove(requirement);
          
          deletedRequirements.push({
            id: requirement.id,
            requirementId: requirement.requirementId,
            name: requirement.name
          });

          this.logger.log(`Successfully deleted requirement ${requirement.requirementId}`);

        } catch (error) {
          this.logger.error(`Failed to delete requirement ${requirement.requirementId}:`, error);
          failedRequirements.push({
            id: requirement.id,
            requirementId: requirement.requirementId,
            name: requirement.name,
            error: error.message || 'Unknown error'
          });
        }
      }

      // Add not found requirements to failed list
      for (const notFoundId of notFoundIds) {
        failedRequirements.push({
          id: notFoundId,
          requirementId: 'Unknown',
          name: 'Unknown',
          error: 'Requirement not found'
        });
      }

      // Step 3: Complete (100%)
      await this.updateProgress(job, 'completed', 100, 'Bulk delete completed', processedCount, requirements.length);

      const result: BulkDeleteJobResult = {
        jobId,
        totalRequirements: requirementIds.length,
        successfulDeletes: deletedRequirements.length,
        failedDeletes: failedRequirements.length,
        deletedRequirements,
        failedRequirements,
        message: `Bulk delete completed. ${deletedRequirements.length} successful, ${failedRequirements.length} failed.`
      };

      this.logger.log(`Bulk delete job ${jobId} completed: ${result.message}`);
      return result;

    } catch (error) {
      this.logger.error(`Bulk delete job ${jobId} failed:`, error);
      throw error;
    }
  }

  private async updateProgress(
    job: Job<BulkDeleteJobData>,
    step: string,
    percentage: number,
    description: string,
    processedCount: number,
    totalCount: number
  ): Promise<void> {
    const progressUpdate: BulkDeleteProgressUpdate = {
      jobId: job.data.jobId,
      step,
      percentage,
      description,
      processedCount,
      totalCount,
      updatedAt: new Date()
    };

    // Update job progress
    await job.updateProgress(progressUpdate);

    // Emit progress via WebSocket if available
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobProgress) {
      this.uploadProgressGateway.broadcastJobProgress(job.data.jobId, progressUpdate);
    }

    this.logger.log(`Job ${job.data.jobId} progress: ${percentage}% - ${description}`);
  }

  /**
   * Delete all related records for a requirement to avoid foreign key constraint violations
   */
  private async deleteRelatedRecords(requirementId: string): Promise<void> {
    try {
      // Delete token usage records
      await this.tokenUsageRepository.delete({ requirementId });
      this.logger.debug(`Deleted token usage records for requirement ${requirementId}`);

      // Delete AI analysis records
      await this.aiAnalysisRepository.delete({ requirementId });
      this.logger.debug(`Deleted AI analysis records for requirement ${requirementId}`);

      // Delete requirement revision records
      await this.requirementRevisionRepository.delete({ requirementId });
      this.logger.debug(`Deleted requirement revision records for requirement ${requirementId}`);

    } catch (error) {
      this.logger.error(`Error deleting related records for requirement ${requirementId}:`, error);
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Bulk delete job ${job.id} completed successfully`);

    // Emit completion via WebSocket
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobCompletion) {
      const { jobId } = job.data;
      this.uploadProgressGateway.broadcastJobCompletion(jobId, job.returnvalue);
    }
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Bulk delete job ${job.id} failed:`, err);

    // Emit failure via WebSocket
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobFailure) {
      const { jobId } = job.data;
      this.uploadProgressGateway.broadcastJobFailure(jobId, err.message);
    }
  }
}
