import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bullmq';
import { Requirement, User, RequirementStatus } from '../../entities';
import { GoogleCloudStorageService } from '../../storage/google-cloud-storage.service';
import { UploadJobData, UploadJobResult } from '../interfaces/upload-job.interface';
import { JobStep } from '../dto/job-status-response.dto';

@Injectable()
@Processor('upload-queue')
export class UploadProcessor extends WorkerHost {
  private readonly logger = new Logger(UploadProcessor.name);

  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
    private googleCloudStorageService: GoogleCloudStorageService,
    @Inject(forwardRef(() => 'UploadProgressGateway'))
    private uploadProgressGateway: any, // Use any to avoid circular dependency issues
  ) {
    super();
  }

  async process(job: Job<UploadJobData>): Promise<UploadJobResult> {
    const { uploadDto, fileBuffer: fileBufferBase64, originalFilename, mimetype, fileContent, jobId } = job.data;

    try {
      this.logger.log(`Processing upload job ${jobId}`);

      // Convert base64 string back to Buffer
      const fileBuffer = Buffer.from(fileBufferBase64, 'base64');

      // Validate that we have a valid buffer
      if (!fileBuffer || fileBuffer.length === 0) {
        throw new Error('Invalid file buffer received');
      }

      // Step 1: Validation (5%)
      await this.updateProgress(job, JobStep.VALIDATING, 5, 'Validating upload data...');
      
      // Find or create user
      let user = await this.userRepository.findOne({
        where: { userId: uploadDto.user_id }
      });

      if (!user) {
        user = this.userRepository.create({
          userId: uploadDto.user_id,
          companyId: uploadDto.company_id,
          projectId: uploadDto.project_id,
        });
        await this.userRepository.save(user);
      }

      // Get draft status ID
      const draftStatus = await this.requirementStatusRepository.findOne({
        where: { name: 'draft' }
      });

      if (!draftStatus) {
        throw new Error('Draft status not found. Please ensure requirement statuses are seeded.');
      }

      // Step 2: Generate requirement ID (15%)
      await this.updateProgress(job, JobStep.VALIDATING, 15, 'Generating requirement ID...');
      
      const count = await this.requirementRepository.count();
      const requirementId = `R-${String(count + 1).padStart(3, '0')}`;

      // Step 3: Upload to storage (60%)
      await this.updateProgress(job, JobStep.UPLOADING_TO_STORAGE, 30, 'Uploading file to cloud storage...');
      
      const storageUrl = await this.googleCloudStorageService.uploadRequirementFile(
        fileBuffer,
        originalFilename,
        mimetype,
        requirementId,
      );

      await this.updateProgress(job, JobStep.UPLOADING_TO_STORAGE, 60, 'File uploaded to cloud storage successfully');

      // Step 4: Save to database (80%)
      await this.updateProgress(job, JobStep.SAVING_TO_DATABASE, 80, 'Saving requirement to database...');
      
      const requirement = this.requirementRepository.create({
        requirementId: requirementId,
        name: uploadDto.name,
        content: fileContent,
        statusId: draftStatus.id,
        storageUrl,
        uploadedById: user.id,
      });

      const savedRequirement = await this.requirementRepository.save(requirement);

      // Step 5: Complete (100%)
      await this.updateProgress(job, JobStep.COMPLETED, 100, 'Upload completed successfully');

      this.logger.log(`Upload job ${jobId} completed successfully`);

      return {
        id: savedRequirement.id,
        requirement_id: savedRequirement.requirementId,
        status: 'draft',
        storageUrl: savedRequirement.storageUrl,
        message: 'File uploaded and processed successfully'
      };

    } catch (error) {
      this.logger.error(`Upload job ${jobId} failed:`, error);
      throw error;
    }
  }

  private async updateProgress(job: Job, step: JobStep, percentage: number, description: string) {
    const progressData = {
      step,
      percentage,
      description,
      updatedAt: new Date()
    };

    await job.updateProgress(progressData);

    // Emit real-time update via WebSocket
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobUpdate) {
      const { jobId } = job.data;
      this.uploadProgressGateway.broadcastJobUpdate(jobId, {
        jobId,
        status: 'processing',
        progress: {
          currentStep: step,
          percentage,
          description,
          updatedAt: progressData.updatedAt
        }
      });
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Job ${job.id} completed successfully`);

    // Emit completion via WebSocket
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobCompletion) {
      const { jobId } = job.data;
      this.uploadProgressGateway.broadcastJobCompletion(jobId, job.returnvalue);
    }
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Job ${job.id} failed:`, err);

    // Emit failure via WebSocket
    if (this.uploadProgressGateway && this.uploadProgressGateway.broadcastJobFailure) {
      const { jobId } = job.data;
      this.uploadProgressGateway.broadcastJobFailure(jobId, err.message);
    }
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: any) {
    this.logger.log(`Job ${job.id} progress: ${progress.percentage}% - ${progress.description}`);
  }
}
