import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiAnalysisService } from '../services/ai-analysis.service';
import { RequirementsService } from '../requirements.service';
import { AiAnalysis, Requirement, TokenUsage } from '../../entities';
import { TokenType } from '../../entities/token-usage.entity';
import { AiAnalysisJobData, AiAnalysisJobResult } from '../services/ai-analysis-queue.service';

@Injectable()
@Processor('ai-analysis')
export class AiAnalysisProcessor extends WorkerHost {
  private readonly logger = new Logger(AiAnalysisProcessor.name);

  constructor(
    private aiAnalysisService: AiAnalysisService,
    private requirementsService: RequirementsService,
    @InjectRepository(AiAnalysis)
    private aiAnalysisRepository: Repository<AiAnalysis>,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    super();
  }

  async process(job: Job<AiAnalysisJobData>): Promise<AiAnalysisJobResult> {
    const { requirementId, userCredentials, jobId } = job.data;
    
    this.logger.log(`Processing AI analysis job ${jobId} for requirement ${requirementId}`);

    try {
      // Step 1: Get requirement from database
      await job.updateProgress(10);
      this.logger.log(`Fetching requirement data for ${requirementId}`);
      
      const requirement = await this.requirementsService.findOne(requirementId);
      if (!requirement) {
        throw new Error(`Requirement ${requirementId} not found`);
      }

      // Step 2: Validate requirement has content
      await job.updateProgress(20);
      if (!requirement.content || requirement.content.trim() === '') {
        throw new Error(`Requirement ${requirementId} has no content to analyze`);
      }

      // Step 3: Prepare content for AI analysis
      await job.updateProgress(30);
      this.logger.log(`Starting AI analysis for requirement ${requirementId}`);

      // For image-heavy PDFs, prioritize context over basic content
      let analysisContent = requirement.content || '';
      const contextData = requirement.context || '';

      // If context contains LLM analysis, use it as primary content for analysis
      if (contextData.includes('=== LLM ANALYSIS ===')) {
        analysisContent = contextData;
        this.logger.log(`Using enhanced context for AI analysis (contains LLM insights)`);
      } else if (contextData && contextData.length > analysisContent.length) {
        // If context has more content than basic content, use context
        analysisContent = contextData;
        this.logger.log(`Using context as primary content (richer than basic content)`);
      }

      const analysisResult = await this.aiAnalysisService.analyzeRequirement(
        analysisContent,
        contextData,
        requirement.embeddingVector || [],
        requirementId
      );

      await job.updateProgress(70);

      // Step 4: Save analysis to database
      this.logger.log(`Saving AI analysis results for requirement ${requirementId}`);
      
      const aiAnalysis = new AiAnalysis();
      aiAnalysis.requirementId = requirementId;
      aiAnalysis.aiQualityScore = analysisResult.qualityScore;
      aiAnalysis.aiFeedback = analysisResult.feedback;
      aiAnalysis.analysisDate = new Date();

      const savedAnalysis = await this.aiAnalysisRepository.save(aiAnalysis);

      await job.updateProgress(85);

      // Step 5: Update requirement metadata and status
      const currentRequirement = await this.requirementsService.findOne(requirementId);
      const updatedMetadata = {
        ...currentRequirement.metadata,
        aiAnalysisStatus: 'completed',
        aiQualityScore: analysisResult.qualityScore,
        aiAnalysisId: savedAnalysis.id,
        lastAnalyzed: new Date().toISOString(),
        analyzedBy: userCredentials,
        aiProvider: analysisResult.provider,
        aiModel: analysisResult.model,
        overallFeedback: analysisResult.overallFeedback,
        criticalIssues: analysisResult.criticalIssues,
        recommendations: analysisResult.recommendations
      };

      await this.requirementsService.updateMetadata(requirementId, updatedMetadata);

      // Step 6: Update requirement status to 'reviewed_by_ai'
      await this.requirementsService.updateStatus(requirementId, 'reviewed_by_ai');

      // Step 7: Track token usage
      await this.trackTokenUsage(
        requirementId,
        analysisResult.tokensUsed,
        analysisResult.model,
        analysisResult.provider,
        analysisResult.cost
      );

      await job.updateProgress(100);

      const result: AiAnalysisJobResult = {
        requirementId,
        success: true,
        analysisGenerated: true,
        qualityScore: analysisResult.qualityScore,
        tokensUsed: analysisResult.tokensUsed,
        message: `AI analysis completed successfully. Quality score: ${analysisResult.qualityScore}/100. ${analysisResult.criticalIssues.length} critical issues found.`
      };

      this.logger.log(`AI analysis job ${jobId} completed successfully with quality score: ${analysisResult.qualityScore}`);
      return result;

    } catch (error) {
      this.logger.error(`AI analysis job ${jobId} failed:`, error);
      
      const result: AiAnalysisJobResult = {
        requirementId,
        success: false,
        analysisGenerated: false,
        tokensUsed: 0,
        message: 'AI analysis failed',
        error: error.message
      };

      throw error; // This will mark the job as failed
    }
  }

  private async trackTokenUsage(
    requirementId: string,
    tokensUsed: number,
    model: string,
    provider: string,
    cost?: number
  ): Promise<void> {
    try {
      const tokenUsage = new TokenUsage();
      tokenUsage.requirementId = requirementId;
      tokenUsage.tokenType = TokenType.ANALYSIS;
      tokenUsage.tokensUsed = Math.round(tokensUsed); // Ensure integer
      tokenUsage.model = model;
      tokenUsage.provider = provider;
      tokenUsage.cost = cost || 0;
      tokenUsage.metadata = {
        analysisType: 'ai_quality_analysis',
        timestamp: new Date().toISOString()
      };

      await this.tokenUsageRepository.save(tokenUsage);
      this.logger.log(`Token usage tracked for AI analysis: ${tokensUsed} tokens, model: ${model}, provider: ${provider}, cost: ${cost || 'N/A'}`);
    } catch (error) {
      this.logger.error(`Error tracking token usage for requirement ${requirementId}:`, error);
      // Don't throw error, just log it
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`AI analysis job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`AI analysis job ${job.id} failed:`, err);
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.log(`AI analysis job ${job.id} progress: ${progress}%`);
  }
}
