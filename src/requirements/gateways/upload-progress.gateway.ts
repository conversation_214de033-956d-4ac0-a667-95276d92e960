import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { UploadQueueService } from '../services/upload-queue.service';

@WebSocketGateway({
  cors: {
    origin: ['http://localhost:5174', 'http://localhost:5173', 'https://app.agentq.id', 'https://staging-app.agentq.id'],
    credentials: true,
  },
  namespace: '/upload-progress',
})
export class UploadProgressGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(UploadProgressGateway.name);
  private clientJobSubscriptions = new Map<string, Set<string>>(); // clientId -> Set of jobIds

  constructor(private uploadQueueService: UploadQueueService) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    this.clientJobSubscriptions.set(client.id, new Set());
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.clientJobSubscriptions.delete(client.id);
  }

  @SubscribeMessage('subscribe-to-job')
  async handleSubscribeToJob(
    @MessageBody() data: { jobId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const { jobId } = data;
    
    if (!jobId) {
      client.emit('error', { message: 'Job ID is required' });
      return;
    }

    // Add job to client's subscriptions
    const clientJobs = this.clientJobSubscriptions.get(client.id);
    if (clientJobs) {
      clientJobs.add(jobId);
    }

    // Join the job-specific room
    client.join(`job-${jobId}`);
    
    // Send current job status
    const status = await this.uploadQueueService.getJobStatus(jobId);
    if (status) {
      client.emit('job-status-update', status);
    } else {
      client.emit('error', { message: 'Job not found' });
    }

    this.logger.log(`Client ${client.id} subscribed to job ${jobId}`);
  }

  @SubscribeMessage('unsubscribe-from-job')
  handleUnsubscribeFromJob(
    @MessageBody() data: { jobId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const { jobId } = data;
    
    if (!jobId) {
      client.emit('error', { message: 'Job ID is required' });
      return;
    }

    // Remove job from client's subscriptions
    const clientJobs = this.clientJobSubscriptions.get(client.id);
    if (clientJobs) {
      clientJobs.delete(jobId);
    }

    // Leave the job-specific room
    client.leave(`job-${jobId}`);
    
    this.logger.log(`Client ${client.id} unsubscribed from job ${jobId}`);
  }

  // Method to broadcast job progress updates (called from the processor)
  broadcastJobUpdate(jobId: string, status: any) {
    this.server.to(`job-${jobId}`).emit('job-status-update', status);
    this.logger.log(`Broadcasted update for job ${jobId} to room job-${jobId}`);
  }

  // Method to broadcast job completion
  broadcastJobCompletion(jobId: string, result: any) {
    this.server.to(`job-${jobId}`).emit('job-completed', { jobId, result });
    this.logger.log(`Broadcasted completion for job ${jobId}`);
  }

  // Method to broadcast job failure
  broadcastJobFailure(jobId: string, error: any) {
    this.server.to(`job-${jobId}`).emit('job-failed', { jobId, error });
    this.logger.log(`Broadcasted failure for job ${jobId}`);
  }
}
