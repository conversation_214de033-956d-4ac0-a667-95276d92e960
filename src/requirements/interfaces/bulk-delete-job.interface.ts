export interface BulkDeleteJobData {
  requirementIds: string[];
  userId?: string;
  reason?: string;
  deleteFiles: boolean;
  jobId: string;
}

export interface BulkDeleteJobResult {
  jobId: string;
  totalRequirements: number;
  successfulDeletes: number;
  failedDeletes: number;
  deletedRequirements: {
    id: string;
    requirementId: string;
    name: string;
  }[];
  failedRequirements: {
    id: string;
    requirementId: string;
    name: string;
    error: string;
  }[];
  message: string;
}

export interface BulkDeleteProgressUpdate {
  jobId: string;
  step: string;
  percentage: number;
  description: string;
  processedCount: number;
  totalCount: number;
  updatedAt: Date;
}
