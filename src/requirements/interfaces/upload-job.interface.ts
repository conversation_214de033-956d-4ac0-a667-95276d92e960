export interface UploadJobData {
  uploadDto: {
    name: string;
    user_id: string;
    company_id: string;
    project_id: string;
  };
  fileBuffer: string; // Base64 encoded string for Redis serialization
  originalFilename: string;
  mimetype: string;
  fileContent: string;
  jobId: string;
}

export interface UploadJobResult {
  id: string;
  requirement_id: string;
  status: string;
  storageUrl: string;
  message: string;
}

export interface JobProgressUpdate {
  jobId: string;
  step: string;
  percentage: number;
  description: string;
  updatedAt: Date;
}
