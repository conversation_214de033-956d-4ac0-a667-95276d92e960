import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RequirementResponseDto {
  @ApiProperty({ description: 'Unique requirement ID' })
  id: string;

  @ApiProperty({ description: 'AgentQ requirement identifier (e.g., R-001)' })
  requirement_id: string;

  @ApiProperty({ description: 'Name of the requirement' })
  name: string;

  @ApiProperty({ description: 'Current status of the requirement' })
  status: string;

  @ApiPropertyOptional({ description: 'Google Cloud Storage URL of the uploaded file' })
  storage_url?: string;

  @ApiProperty({ description: 'User ID who uploaded the requirement' })
  uploaded_by: string;

  @ApiProperty({ description: 'Creation timestamp' })
  created_at: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updated_at: Date;

  @ApiPropertyOptional({ description: 'Requirement content (only included in detailed view)' })
  content?: string;
}

export class UploadResponseDto {
  @ApiProperty({ description: 'Unique requirement ID' })
  id: string;

  @ApiProperty({ description: 'AgentQ requirement identifier (e.g., R-001)' })
  requirement_id: string;

  @ApiProperty({ description: 'Current status of the requirement' })
  status: string;

  @ApiProperty({ description: 'Message indicating the file is being processed' })
  message: string;
}

export class PaginatedRequirementsResponseDto {
  @ApiProperty({ type: [RequirementResponseDto] })
  data: RequirementResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
