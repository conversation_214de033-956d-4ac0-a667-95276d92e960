import { IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class QueryRequirementsDto {
  @ApiPropertyOptional({ 
    description: 'Filter by requirement status',
    enum: ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected']
  })
  @IsOptional()
  @IsString()
  @IsIn(['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'])
  status?: string;

  @ApiPropertyOptional({ description: 'Filter by project ID' })
  @IsOptional()
  @IsUUID()
  project_id?: string;

  @ApiPropertyOptional({ 
    description: 'Sort by field',
    enum: ['created_at', 'updated_at', 'status', 'name']
  })
  @IsOptional()
  @IsString()
  @IsIn(['created_at', 'updated_at', 'status', 'name'])
  sort_by?: string;

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC']
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  order?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: 'Page number for pagination', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}
