import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, MaxLength, IsArray, IsNumber, IsUUID } from 'class-validator';

export enum EmbeddingProvider {
  OPENAI = 'openai',
  GEMINI = 'gemini'
}

export enum EmbeddingModel {
  // OpenAI models
  OPENAI_ADA_002 = 'text-embedding-ada-002',
  OPENAI_3_SMALL = 'text-embedding-3-small',
  OPENAI_3_LARGE = 'text-embedding-3-large',
  
  // Gemini models
  GEMINI_EMBEDDING = 'models/embedding-001'
}

export class GenerateEmbeddingDto {
  @ApiProperty({
    description: 'User ID from users table',
    example: 'user-uuid-123'
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'Company ID from users table',
    example: 'company-uuid-456'
  })
  @IsString()
  @IsNotEmpty()
  company_id: string;

  @ApiProperty({
    description: 'Project ID from users table',
    example: 'project-uuid-789'
  })
  @IsString()
  @IsNotEmpty()
  project_id: string;

  @ApiProperty({
    description: 'Requirement UUID from requirements table to process',
    example: '4be657e1-9b65-4ce0-be82-9a9dd2953841'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class EmbeddingResponseDto {
  @ApiProperty({
    description: 'Job ID for tracking embedding generation progress',
    example: 'file_embedding_12345678-1234-1234-1234-123456789012'
  })
  @IsString()
  jobId: string;

  @ApiProperty({
    description: 'Message about the embedding generation status',
    example: 'Embedding generation started. Use the status endpoint to check progress.'
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Current status of the embedding job',
    example: 'queued'
  })
  @IsString()
  status: string;
}

export class EmbeddingStatusDto {
  @ApiProperty({
    description: 'Requirement ID',
    example: '4be657e1-9b65-4ce0-be82-9a9dd2953841'
  })
  @IsString()
  requirementId: string;

  @ApiProperty({
    description: 'Current embedding status',
    example: 'completed',
    enum: ['not_started', 'in_progress', 'completed', 'failed']
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Job ID for tracking',
    example: 'file_embedding_12345678-1234-1234-1234-123456789012',
    required: false
  })
  @IsOptional()
  @IsString()
  jobId?: string;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 75,
    required: false
  })
  @IsOptional()
  @IsNumber()
  progress?: number;

  @ApiProperty({
    description: 'Error message if failed',
    required: false
  })
  @IsOptional()
  @IsString()
  error?: string;

  @ApiProperty({
    description: 'Completion timestamp',
    example: '2024-01-15T10:30:00Z',
    required: false
  })
  @IsOptional()
  @IsString()
  completedAt?: string;

  @ApiProperty({
    description: 'Additional metadata about the embedding',
    required: false
  })
  @IsOptional()
  metadata?: {
    hasEmbedding: boolean;
    embeddingStatus: string;
    processingMethod: string;
    llmSummary: string;
    hasImages: boolean;
    hasDiagrams: boolean;
    hasComplexLayout: boolean;
    processedBy: {
      userId: string;
      companyId: string;
      projectId: string;
    };
  };
}

export class SimilaritySearchDto {
  @ApiProperty({
    description: 'Text query to search for similar requirements',
    example: 'user authentication and login functionality',
    maxLength: 1000
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  query: string;

  @ApiProperty({
    description: 'Maximum number of similar requirements to return',
    example: 10,
    minimum: 1,
    maximum: 50,
    required: false
  })
  @IsOptional()
  @IsNumber()
  limit?: number = 10;

  @ApiProperty({
    description: 'Minimum similarity threshold (0-1)',
    example: 0.7,
    minimum: 0,
    maximum: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  threshold?: number = 0.7;

  @ApiProperty({
    description: 'Embedding provider to use for query embedding',
    enum: EmbeddingProvider,
    example: EmbeddingProvider.OPENAI,
    required: false
  })
  @IsOptional()
  @IsEnum(EmbeddingProvider)
  provider?: EmbeddingProvider;
}

export class SimilarityResultDto {
  @ApiProperty({
    description: 'Requirement information'
  })
  requirement: {
    id: string;
    requirementId: string;
    name: string;
    content: string;
    metadata?: Record<string, any>;
  };

  @ApiProperty({
    description: 'Similarity score (0-1, higher is more similar)',
    example: 0.85
  })
  @IsNumber()
  similarity: number;
}
