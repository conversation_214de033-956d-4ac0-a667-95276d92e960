import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsIn } from 'class-validator';

export class UpdateStatusDto {
  @ApiProperty({
    description: 'New status for the requirement',
    example: 'reviewed_by_ai',
    enum: ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected']
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'])
  status: string;
}
