import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsO<PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class GenerateAiAnalysisDto {
  @ApiProperty({
    description: 'User ID requesting the analysis',
    example: 'aca4c5be-2b12-4e9e-b6e6-f96638d8e1d6'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  user_id: string;

  @ApiProperty({
    description: 'Company ID',
    example: '704f4925-9bd3-46b4-8bce-28f8671ef482'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  company_id: string;

  @ApiProperty({
    description: 'Project ID',
    example: 'f907b2b1-4347-480c-8bc5-0b669649599a'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  project_id: string;

  @ApiProperty({
    description: 'Requirement ID to analyze',
    example: '4be657e1-9b65-4ce0-be82-9a9dd2953841'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class AiAnalysisResponseDto {
  @ApiProperty({
    description: 'Job ID for tracking AI analysis progress',
    example: 'ai_analysis_12345678-1234-1234-1234-123456789012'
  })
  @IsString()
  jobId: string;

  @ApiProperty({
    description: 'Message about the AI analysis status',
    example: 'AI analysis started. Use the status endpoint to check progress.'
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Current status of the analysis job',
    example: 'queued'
  })
  @IsString()
  status: string;
}

export class AiAnalysisStatusDto {
  @ApiProperty({
    description: 'Requirement ID',
    example: '4be657e1-9b65-4ce0-be82-9a9dd2953841'
  })
  @IsString()
  requirementId: string;

  @ApiProperty({
    description: 'Current analysis status',
    example: 'completed',
    enum: ['not_started', 'in_progress', 'completed', 'failed']
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Job ID for tracking',
    example: 'ai_analysis_12345678-1234-1234-1234-123456789012',
    required: false
  })
  @IsOptional()
  @IsString()
  jobId?: string;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 75,
    required: false
  })
  @IsOptional()
  @IsNumber()
  progress?: number;

  @ApiProperty({
    description: 'Error message if failed',
    required: false
  })
  @IsOptional()
  @IsString()
  error?: string;

  @ApiProperty({
    description: 'Completion timestamp',
    example: '2024-01-15T10:30:00Z',
    required: false
  })
  @IsOptional()
  @IsString()
  completedAt?: string;

  @ApiProperty({
    description: 'AI analysis results',
    required: false
  })
  @IsOptional()
  analysis?: {
    id: string;
    aiQualityScore: number;
    aiFeedback: any;
    analysisDate: string;
  };
}

export class AiAnalysisResultDto {
  @ApiProperty({
    description: 'Analysis ID',
    example: '12345678-1234-1234-1234-123456789012'
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Requirement ID that was analyzed',
    example: '4be657e1-9b65-4ce0-be82-9a9dd2953841'
  })
  @IsString()
  requirementId: string;

  @ApiProperty({
    description: 'AI quality score (1-100)',
    example: 85,
    minimum: 1,
    maximum: 100
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  aiQualityScore: number;

  @ApiProperty({
    description: 'Structured AI feedback',
    example: {
      clarity: { score: 8, feedback: "Requirements are well-defined" },
      completeness: { score: 7, feedback: "Missing edge cases" },
      consistency: { score: 9, feedback: "Consistent terminology" },
      testability: { score: 6, feedback: "Acceptance criteria could be more specific" }
    }
  })
  aiFeedback: any;

  @ApiProperty({
    description: 'Analysis timestamp',
    example: '2024-01-15T10:30:00Z'
  })
  @IsString()
  analysisDate: string;
}
