import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Option<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UploadRequirementDto {
  @ApiProperty({ description: 'Name of the requirement document' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Project ID associated with the requirement' })
  @IsUUID()
  @IsNotEmpty()
  project_id: string;

  @ApiProperty({ description: 'Company ID associated with the requirement' })
  @IsUUID()
  @IsNotEmpty()
  company_id: string;

  @ApiProperty({ description: 'User ID uploading the requirement' })
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({ 
    type: 'string', 
    format: 'binary', 
    description: 'The requirement document file' 
  })
  file: any;
}
