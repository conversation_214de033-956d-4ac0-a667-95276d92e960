import { ApiProperty } from '@nestjs/swagger';

export class UploadJobResponseDto {
  @ApiProperty({
    description: 'Unique job ID for tracking upload progress',
    example: 'upload_123e4567-e89b-12d3-a456-426614174000'
  })
  jobId: string;

  @ApiProperty({
    description: 'Initial status of the upload job',
    example: 'queued'
  })
  status: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Upload job created successfully. Use jobId to track progress.'
  })
  message: string;

  @ApiProperty({
    description: 'Estimated processing time in seconds',
    example: 30
  })
  estimatedProcessingTime: number;
}
