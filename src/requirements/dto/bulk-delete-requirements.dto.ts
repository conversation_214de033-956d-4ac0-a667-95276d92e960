import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, ArrayMinSize, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BulkDeleteRequirementsDto {
  @ApiProperty({ 
    description: 'Array of requirement IDs to delete',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-b789-123456789abc']
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one requirement ID must be provided' })
  @IsUUID('4', { each: true, message: 'Each requirement ID must be a valid UUID' })
  @IsNotEmpty({ each: true })
  requirementIds: string[];

  @ApiPropertyOptional({ 
    description: 'User ID performing the bulk delete operation',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID('4')
  userId?: string;

  @ApiPropertyOptional({ 
    description: 'Reason for bulk deletion (optional)',
    example: 'Cleanup of outdated requirements'
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Whether to also delete associated files from storage',
    default: true
  })
  @IsOptional()
  deleteFiles?: boolean = true;
}

export class BulkDeleteJobResponseDto {
  @ApiProperty({
    description: 'Unique job ID for tracking bulk delete progress',
    example: 'bulk_delete_123e4567-e89b-12d3-a456-************'
  })
  jobId: string;

  @ApiProperty({
    description: 'Initial status of the bulk delete job',
    example: 'queued'
  })
  status: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Bulk delete job created successfully. Use jobId to track progress.'
  })
  message: string;

  @ApiProperty({
    description: 'Number of requirements to be deleted',
    example: 5
  })
  totalRequirements: number;

  @ApiProperty({
    description: 'Estimated processing time in seconds',
    example: 45
  })
  estimatedProcessingTime: number;
}
