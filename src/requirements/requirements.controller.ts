import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Query,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { RequirementsService } from './requirements.service';
import { DatabaseTestService } from './database-test.service';
import { UploadQueueService } from './services/upload-queue.service';
import { BulkDeleteQueueService } from './services/bulk-delete-queue.service';
import { EmbeddingQueueService } from './services/embedding-queue.service';
import { AiAnalysisQueueService } from './services/ai-analysis-queue.service';
import { HybridFileProcessorService } from './services/hybrid-file-processor.service';
import { EmbeddingService } from './services/embedding.service';
import { FileSummarizationService } from './services/file-summarization.service';
import { AiAnalysisService } from './services/ai-analysis.service';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import {
  UploadRequirementDto,
  QueryRequirementsDto,
  UploadResponseDto,
  PaginatedRequirementsResponseDto,
  RequirementResponseDto,
  UploadJobResponseDto,
  JobStatusResponseDto,
  BulkDeleteRequirementsDto,
  BulkDeleteJobResponseDto,
  GenerateEmbeddingDto,
  EmbeddingResponseDto,
  EmbeddingStatusDto,
  GenerateAiAnalysisDto,
  AiAnalysisResponseDto,
  AiAnalysisStatusDto,
  SimilaritySearchDto,
  SimilarityResultDto,
} from './dto';

@ApiTags('requirements')
@Controller('api/v1/requirements')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RequirementsController {
  private readonly logger = new Logger(RequirementsController.name);

  constructor(
    private readonly requirementsService: RequirementsService,
    private readonly databaseTestService: DatabaseTestService,
    private readonly uploadQueueService: UploadQueueService,
    private readonly bulkDeleteQueueService: BulkDeleteQueueService,
    private readonly embeddingQueueService: EmbeddingQueueService,
    private readonly aiAnalysisQueueService: AiAnalysisQueueService,
    private readonly hybridFileProcessorService: HybridFileProcessorService,
    private readonly embeddingService: EmbeddingService,
    private readonly fileSummarizationService: FileSummarizationService,
    private readonly aiAnalysisService: AiAnalysisService,
    private readonly googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  @Post('upload/async')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload requirement file asynchronously' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload requirement file with metadata',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The requirement file to upload'
        },
        name: {
          type: 'string',
          description: 'Name of the requirement'
        },
        user_id: {
          type: 'string',
          description: 'ID of the user uploading the file'
        },
        company_id: {
          type: 'string',
          description: 'ID of the company'
        },
        project_id: {
          type: 'string',
          description: 'ID of the project'
        }
      },
      required: ['file', 'name', 'user_id', 'company_id', 'project_id']
    }
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Upload job created successfully',
    type: UploadJobResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async uploadAsync(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadRequirementDto,
  ): Promise<UploadJobResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Extract file content based on file type
    let fileContent: string;
    try {
      if (file.mimetype === 'application/pdf') {
        const pdfParse = require('pdf-parse');
        const data = await pdfParse(file.buffer);
        fileContent = data.text;
      } else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const mammoth = require('mammoth');
        const result = await mammoth.extractRawText({ buffer: file.buffer });
        fileContent = result.value;
      } else if (file.mimetype === 'text/plain') {
        fileContent = file.buffer.toString('utf-8');
      } else if (file.mimetype === 'text/markdown') {
        fileContent = file.buffer.toString('utf-8');
      } else {
        throw new BadRequestException('Unsupported file type. Please upload PDF, DOCX, or TXT files.');
      }
    } catch (error) {
      throw new BadRequestException('Failed to extract content from file');
    }

    return this.requirementsService.uploadRequirementAsync(
      uploadDto,
      fileContent,
      file.buffer,
      file.originalname,
      file.mimetype,
    );
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a new requirement document' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'File uploaded successfully and is being processed asynchronously',
    type: UploadResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async uploadRequirement(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadRequirementDto,
  ): Promise<UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Validate file type
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Unsupported file type. Supported types: PDF, Word, Text, Markdown, HTML, Excel, CSV'
      );
    }

    // Extract text content from file
    const fileContent = this.extractTextFromFile(file.buffer, file.mimetype, file.originalname);

    const result = await this.requirementsService.uploadRequirement(
      uploadDto,
      fileContent,
      file.buffer,
      file.originalname,
      file.mimetype,
    );

    return result;
  }

  @Get()
  @ApiOperation({ summary: 'List all requirements with filtering and pagination' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by requirement status' })
  @ApiQuery({ name: 'project_id', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'sort_by', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order (ASC/DESC)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of requirements retrieved successfully',
    type: PaginatedRequirementsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findAll(@Query() queryDto: QueryRequirementsDto): Promise<PaginatedRequirementsResponseDto> {
    return this.requirementsService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific requirement by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Requirement retrieved successfully',
    type: RequirementResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findOne(@Param('id') id: string): Promise<RequirementResponseDto> {
    return this.requirementsService.findOne(id);
  }

  @Get('jobs/:jobId/status')
  @ApiOperation({ summary: 'Get upload job status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job status retrieved successfully',
    type: JobStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getJobStatus(@Param('jobId') jobId: string): Promise<JobStatusResponseDto> {
    const status = await this.uploadQueueService.getJobStatus(jobId);
    if (!status) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return status;
  }

  @Delete('jobs/:jobId')
  @ApiOperation({ summary: 'Cancel upload job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job cancelled successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async cancelJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const cancelled = await this.uploadQueueService.cancelJob(jobId);
    if (!cancelled) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return { message: 'Job cancelled successfully' };
  }

  @Post('jobs/:jobId/retry')
  @ApiOperation({ summary: 'Retry failed upload job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job retried successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async retryJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const retried = await this.uploadQueueService.retryFailedJob(jobId);
    if (!retried) {
      throw new NotFoundException(`Job with ID ${jobId} not found or cannot be retried`);
    }
    return { message: 'Job retried successfully' };
  }

  @Get('queue/stats')
  @ApiOperation({ summary: 'Get upload queue statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue statistics retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getQueueStats() {
    return this.uploadQueueService.getQueueStats();
  }

  @Delete('bulk')
  @ApiOperation({ summary: 'Bulk delete requirements asynchronously' })
  @ApiBody({
    description: 'Bulk delete requirements data',
    type: BulkDeleteRequirementsDto,
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Bulk delete job created successfully',
    type: BulkDeleteJobResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request data',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async bulkDeleteRequirements(
    @Body() bulkDeleteDto: BulkDeleteRequirementsDto,
  ): Promise<BulkDeleteJobResponseDto> {
    return this.requirementsService.bulkDeleteRequirementsAsync(bulkDeleteDto);
  }

  @Get('bulk-delete/jobs/:jobId/status')
  @ApiOperation({ summary: 'Get bulk delete job status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job status retrieved successfully',
    type: JobStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getBulkDeleteJobStatus(@Param('jobId') jobId: string): Promise<JobStatusResponseDto> {
    const status = await this.bulkDeleteQueueService.getJobStatus(jobId);
    if (!status) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return status;
  }

  @Delete('bulk-delete/jobs/:jobId')
  @ApiOperation({ summary: 'Cancel bulk delete job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job cancelled successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async cancelBulkDeleteJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const cancelled = await this.bulkDeleteQueueService.cancelJob(jobId);
    if (!cancelled) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return { message: 'Job cancelled successfully' };
  }

  @Post('bulk-delete/jobs/:jobId/retry')
  @ApiOperation({ summary: 'Retry failed bulk delete job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job retried successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async retryBulkDeleteJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const retried = await this.bulkDeleteQueueService.retryFailedJob(jobId);
    if (!retried) {
      throw new NotFoundException(`Job with ID ${jobId} not found or cannot be retried`);
    }
    return { message: 'Job retried successfully' };
  }

  @Get('bulk-delete/queue/stats')
  @ApiOperation({ summary: 'Get bulk delete queue statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue statistics retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getBulkDeleteQueueStats() {
    return this.bulkDeleteQueueService.getQueueStats();
  }



  @Post(':id/embedding/retry')
  @ApiOperation({ summary: 'Retry embedding generation for a requirement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Embedding retry initiated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async retryEmbedding(@Param('id') id: string) {
    const requirement = await this.requirementsService.findOne(id);
    if (!requirement) {
      throw new NotFoundException(`Requirement with ID ${id} not found`);
    }

    const jobId = await this.embeddingQueueService.retryEmbeddingJob(id);
    if (!jobId) {
      throw new BadRequestException('Failed to retry embedding generation');
    }

    return {
      message: 'Embedding retry initiated successfully',
      jobId,
      requirementId: id
    };
  }

  @Get('embeddings/queue/stats')
  @ApiOperation({ summary: 'Get embedding queue statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue statistics retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getEmbeddingQueueStats() {
    return this.embeddingQueueService.getQueueStats();
  }

  @Post('embedding')
  @ApiOperation({ summary: 'Generate embedding for requirement file using LLM summarization' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Embedding generated successfully',
    type: EmbeddingResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to generate embedding',
  })
  async generateEmbedding(@Body() generateEmbeddingDto: GenerateEmbeddingDto): Promise<EmbeddingResponseDto> {
    try {
      // Step 1: Validate user credentials
      this.logger.log(`Validating user credentials: user_id=${generateEmbeddingDto.user_id}, company_id=${generateEmbeddingDto.company_id}, project_id=${generateEmbeddingDto.project_id}`);
      const userExists = await this.requirementsService.validateUserCredentials(
        generateEmbeddingDto.user_id,
        generateEmbeddingDto.company_id,
        generateEmbeddingDto.project_id
      );

      if (!userExists) {
        throw new BadRequestException(
          `Invalid user credentials: user_id=${generateEmbeddingDto.user_id}, company_id=${generateEmbeddingDto.company_id}, project_id=${generateEmbeddingDto.project_id} not found in users table`
        );
      }

      // Step 2: Get requirement from database
      this.logger.log(`Looking for requirement with ID: ${generateEmbeddingDto.id}`);
      const requirement = await this.requirementsService.findOne(generateEmbeddingDto.id);
      if (!requirement) {
        throw new BadRequestException(`Requirement with ID ${generateEmbeddingDto.id} not found`);
      }

      // Step 3: Validate requirement has file
      this.logger.log(`Found requirement: ${requirement.name}, storage_url: ${requirement.storage_url}`);
      if (!requirement.storage_url || requirement.storage_url.trim() === '') {
        throw new BadRequestException(`Requirement ${generateEmbeddingDto.id} has no file attached`);
      }

      // Step 4: Add job to queue for background processing
      const jobId = await this.embeddingQueueService.addFileEmbeddingJob(
        generateEmbeddingDto.id,
        requirement.storage_url,
        {
          userId: generateEmbeddingDto.user_id,
          companyId: generateEmbeddingDto.company_id,
          projectId: generateEmbeddingDto.project_id
        }
      );

      this.logger.log(`Embedding job ${jobId} queued for requirement ${generateEmbeddingDto.id}`);

      return {
        jobId,
        message: 'Embedding generation started. Use the status endpoint to check progress.',
        status: 'queued'
      };

    } catch (error) {
      this.logger.error(`Failed to queue embedding for requirement ${generateEmbeddingDto.id}:`, error);
      throw new BadRequestException(`Failed to queue embedding: ${error.message}`);
    }
  }

  @Get('embedding/status/:id')
  @ApiOperation({ summary: 'Get embedding generation status for a requirement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Embedding status retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  async getEmbeddingStatus(@Param('id') requirementId: string): Promise<EmbeddingStatusDto> {
    try {
      // Get embedding status from queue service
      const embeddingStatus = await this.embeddingQueueService.getRequirementEmbeddingStatus(requirementId);

      // Get requirement metadata for additional info
      const requirement = await this.requirementsService.findOne(requirementId);
      if (!requirement) {
        throw new NotFoundException(`Requirement with ID ${requirementId} not found`);
      }

      return {
        requirementId,
        status: embeddingStatus.status,
        jobId: embeddingStatus.jobId,
        progress: embeddingStatus.progress,
        error: embeddingStatus.error,
        completedAt: requirement.metadata?.lastProcessed,
        metadata: {
          hasEmbedding: !!requirement.metadata?.embeddingStatus,
          embeddingStatus: requirement.metadata?.embeddingStatus,
          processingMethod: requirement.metadata?.processingMethod,
          llmSummary: requirement.metadata?.llmSummary ? 'Available' : 'Not available',
          hasImages: requirement.metadata?.hasImages,
          hasDiagrams: requirement.metadata?.hasDiagrams,
          hasComplexLayout: requirement.metadata?.hasComplexLayout,
          processedBy: requirement.metadata?.processedBy
        }
      };

    } catch (error) {
      this.logger.error(`Failed to get embedding status for requirement ${requirementId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get embedding status: ${error.message}`);
    }
  }

  @Post('ai_analysis')
  @ApiOperation({ summary: 'Generate AI analysis and quality score for a requirement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'AI analysis started successfully',
    type: AiAnalysisResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async generateAiAnalysis(@Body() generateAiAnalysisDto: GenerateAiAnalysisDto): Promise<AiAnalysisResponseDto> {
    try {
      // Step 1: Validate user credentials
      this.logger.log(`Validating user credentials for AI analysis: user_id=${generateAiAnalysisDto.user_id}, company_id=${generateAiAnalysisDto.company_id}, project_id=${generateAiAnalysisDto.project_id}`);
      const userExists = await this.requirementsService.validateUserCredentials(
        generateAiAnalysisDto.user_id,
        generateAiAnalysisDto.company_id,
        generateAiAnalysisDto.project_id
      );

      if (!userExists) {
        throw new BadRequestException(
          `Invalid user credentials: user_id=${generateAiAnalysisDto.user_id}, company_id=${generateAiAnalysisDto.company_id}, project_id=${generateAiAnalysisDto.project_id} not found in users table`
        );
      }

      // Step 2: Get requirement from database
      this.logger.log(`Looking for requirement with ID: ${generateAiAnalysisDto.id}`);
      const requirement = await this.requirementsService.findOne(generateAiAnalysisDto.id);
      if (!requirement) {
        throw new BadRequestException(`Requirement with ID ${generateAiAnalysisDto.id} not found`);
      }

      // Step 3: Validate requirement has content
      if (!requirement.content || requirement.content.trim() === '') {
        throw new BadRequestException(`Requirement ${generateAiAnalysisDto.id} has no content to analyze`);
      }

      // Step 4: Add job to queue for background processing
      const jobId = await this.aiAnalysisQueueService.addAiAnalysisJob(
        generateAiAnalysisDto.id,
        {
          userId: generateAiAnalysisDto.user_id,
          companyId: generateAiAnalysisDto.company_id,
          projectId: generateAiAnalysisDto.project_id
        }
      );

      this.logger.log(`AI analysis job ${jobId} queued for requirement ${generateAiAnalysisDto.id}`);

      return {
        jobId,
        message: 'AI analysis started. Use the status endpoint to check progress.',
        status: 'queued'
      };

    } catch (error) {
      this.logger.error(`Failed to queue AI analysis for requirement ${generateAiAnalysisDto.id}:`, error);
      throw new BadRequestException(`Failed to queue AI analysis: ${error.message}`);
    }
  }

  @Get('ai_analysis/status/:id')
  @ApiOperation({ summary: 'Get AI analysis status for a requirement' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'AI analysis status retrieved successfully',
    type: AiAnalysisStatusDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  async getAiAnalysisStatus(@Param('id') requirementId: string): Promise<AiAnalysisStatusDto> {
    try {
      // Get AI analysis status from queue service
      const analysisStatus = await this.aiAnalysisQueueService.getRequirementAnalysisStatus(requirementId);

      // Get requirement metadata for additional info
      const requirement = await this.requirementsService.findOne(requirementId);
      if (!requirement) {
        throw new NotFoundException(`Requirement with ID ${requirementId} not found`);
      }

      return {
        requirementId,
        status: analysisStatus.status,
        jobId: analysisStatus.jobId,
        progress: analysisStatus.progress,
        error: analysisStatus.error,
        completedAt: requirement.metadata?.lastAnalyzed,
        analysis: analysisStatus.analysis
      };

    } catch (error) {
      this.logger.error(`Failed to get AI analysis status for requirement ${requirementId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get AI analysis status: ${error.message}`);
    }
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update requirement status' })
  @ApiBody({
    description: 'Status update data',
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'],
          example: 'reviewed_by_ai'
        }
      },
      required: ['status']
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Requirement status updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement or status not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid status name',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async updateRequirementStatus(
    @Param('id') requirementId: string,
    @Body() updateStatusDto: { status: string }
  ): Promise<{ message: string }> {
    try {
      await this.requirementsService.updateStatus(requirementId, updateStatusDto.status);
      return { message: `Requirement status updated to '${updateStatusDto.status}' successfully` };
    } catch (error) {
      this.logger.error(`Failed to update status for requirement ${requirementId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update requirement status: ${error.message}`);
    }
  }

  @Post('embedding/search')
  @ApiOperation({ summary: 'Search for similar requirements using semantic similarity' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Similar requirements found successfully',
    type: [SimilarityResultDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid search parameters',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async searchSimilarRequirements(@Body() searchDto: SimilaritySearchDto): Promise<SimilarityResultDto[]> {
    try {
      // First, generate embedding for the search query
      const queryEmbedding = await this.embeddingService.generateEmbedding(
        searchDto.query,
        'Similarity search query'
      );

      // Search for similar requirements
      const results = await this.embeddingService.searchSimilarRequirements(
        queryEmbedding.vector,
        searchDto.limit || 10,
        searchDto.threshold || 0.7
      );

      return results.map(result => ({
        requirement: {
          id: result.requirement.id,
          requirementId: result.requirement.requirementId,
          name: result.requirement.name,
          content: result.requirement.content,
          metadata: result.requirement.metadata,
        },
        similarity: result.similarity,
      }));

    } catch (error) {
      throw new BadRequestException(`Failed to search similar requirements: ${error.message}`);
    }
  }

  @Get('embedding/models')
  @ApiOperation({ summary: 'Get available embedding models and their specifications' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Available embedding models retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getAvailableEmbeddingModels() {
    return {
      providers: {
        openai: {
          models: [
            {
              name: 'text-embedding-ada-002',
              dimensions: 1536,
              maxTokens: 8191,
              costPer1kTokens: 0.0001,
              description: 'Most capable embedding model for most tasks'
            },
            {
              name: 'text-embedding-3-small',
              dimensions: 1536,
              maxTokens: 8191,
              costPer1kTokens: 0.00002,
              description: 'Improved performance over ada-002'
            },
            {
              name: 'text-embedding-3-large',
              dimensions: 3072,
              maxTokens: 8191,
              costPer1kTokens: 0.00013,
              description: 'Most capable embedding model'
            }
          ]
        },
        gemini: {
          models: [
            {
              name: 'models/embedding-001',
              dimensions: 768,
              maxTokens: 2048,
              costPer1kTokens: 0.0000125,
              description: 'Google Gemini embedding model'
            }
          ]
        }
      },
      defaultProvider: process.env.EMBEDDING_PROVIDER || 'openai',
      defaultModel: process.env.EMBEDDING_MODEL || 'text-embedding-ada-002'
    };
  }

  private extractTextFromFile(buffer: Buffer, mimetype: string, originalname: string): string {
    // For now, return a simple text representation
    // In a real implementation, you would use libraries like:
    // - mammoth for .docx files
    // - pdf-parse for PDF files
    // - xlsx for Excel files
    // etc.
    
    if (mimetype.includes('text/') || originalname.endsWith('.md') || originalname.endsWith('.txt')) {
      return buffer.toString('utf-8');
    }
    
    // For other file types, return a placeholder
    return `[File content extracted from ${originalname}]\n\nThis is a placeholder for the actual file content that would be extracted using appropriate libraries for ${mimetype} files.`;
  }

  private getMimetypeFromFilename(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();

    const mimetypeMap: Record<string, string> = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'md': 'text/markdown',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'xls': 'application/vnd.ms-excel',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'svg': 'image/svg+xml'
    };

    return mimetypeMap[extension || ''] || 'application/octet-stream';
  }
}
