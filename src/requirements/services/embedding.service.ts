import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, TokenUsage, TokenType, EmbeddingStatus } from '../../entities';

interface EmbeddingResult {
  vector: number[];
  tokensUsed: number;
  model: string;
  provider: string;
  cost?: number;
}

@Injectable()
export class EmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);
  private readonly embeddingProvider: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    // Use the same LLM provider for embedding as configured in LLM environment variable
    this.embeddingProvider = this.configService.get<string>('LLM', 'OPENAI');
  }

  async generateEmbedding(
    content: string,
    requirementId: string,
    additionalContext?: string
  ): Promise<EmbeddingResult> {
    this.logger.log(`Generating embedding for requirement ${requirementId}`);

    try {
      // Prepare content for embedding
      const embeddingText = this.prepareTextForEmbedding(content, additionalContext);
      
      let result: EmbeddingResult;

      if (this.embeddingProvider === 'OPENAI') {
        result = await this.generateOpenAIEmbedding(embeddingText);
      } else if (this.embeddingProvider === 'GEMINI') {
        result = await this.generateGeminiEmbedding(embeddingText);
      } else {
        throw new Error(`Unsupported embedding provider: ${this.embeddingProvider}`);
      }

      // Record token usage
      await this.recordTokenUsage(requirementId, result.tokensUsed, result.model, result.provider);

      this.logger.log(`Embedding generated successfully for requirement ${requirementId}`);
      return result;

    } catch (error) {
      this.logger.error(`Error generating embedding for requirement ${requirementId}:`, error);
      throw error;
    }
  }

  async updateRequirementEmbedding(
    requirementId: string,
    embeddingResult: EmbeddingResult
  ): Promise<void> {
    try {
      // Get current metadata to preserve existing data
      const currentRequirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      const currentMetadata = currentRequirement?.metadata || {};

      const updatedMetadata: Record<string, any> = {
        ...currentMetadata,
        embeddingStatus: EmbeddingStatus.COMPLETED,
        embeddingModel: embeddingResult.model,
        embeddingProvider: embeddingResult.provider,
        processingTimestamp: new Date().toISOString(),
      };

      await this.requirementRepository.update(requirementId, {
        embeddingVector: embeddingResult.vector, // Store as proper vector type
        metadata: updatedMetadata as any
      });

      this.logger.log(`Updated embedding for requirement ${requirementId}`);
    } catch (error) {
      this.logger.error(`Error updating embedding for requirement ${requirementId}:`, error);
      
      // Mark as failed - preserve existing metadata
      const currentRequirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      const currentMetadata = currentRequirement?.metadata || {};

      const failedMetadata: Record<string, any> = {
        ...currentMetadata,
        embeddingStatus: EmbeddingStatus.FAILED,
        errorDetails: error.message,
        processingTimestamp: new Date().toISOString(),
      };

      await this.requirementRepository.update(requirementId, {
        metadata: failedMetadata as any
      });
      
      throw error;
    }
  }

  async searchSimilarRequirements(
    queryVector: number[],
    limit: number = 10,
    threshold: number = 0.7
  ): Promise<Array<{ requirement: Requirement; similarity: number }>> {
    try {
      // Using PostgreSQL pgvector extension for similarity search
      const query = `
        SELECT 
          r.*,
          1 - (r.embedding_vector <=> $1::vector) as similarity
        FROM requirements r
        WHERE r.embedding_vector IS NOT NULL
          AND 1 - (r.embedding_vector <=> $1::vector) > $2
        ORDER BY r.embedding_vector <=> $1::vector
        LIMIT $3
      `;

      const results = await this.requirementRepository.query(query, [
        `[${queryVector.join(',')}]`, // Format as vector literal for pgvector
        threshold,
        limit
      ]);

      return results.map((row: any) => ({
        requirement: row,
        similarity: parseFloat(row.similarity)
      }));

    } catch (error) {
      this.logger.error('Error searching similar requirements:', error);
      throw error;
    }
  }

  async trackTokenUsage(
    requirementId: string,
    tokenType: string,
    tokensUsed: number,
    model: string,
    provider: string,
    cost?: number
  ): Promise<void> {
    try {
      const tokenUsage = this.tokenUsageRepository.create({
        requirementId,
        tokenType: tokenType as TokenType,
        tokensUsed,
        model,
        provider,
        cost,
        metadata: {
          timestamp: new Date().toISOString(),
          model,
          provider
        }
      });

      await this.tokenUsageRepository.save(tokenUsage);
      this.logger.log(`Token usage tracked: ${tokensUsed} tokens for ${tokenType} using ${model}`);

    } catch (error) {
      this.logger.error(`Error tracking token usage for requirement ${requirementId}:`, error);
      // Don't throw error - token tracking failure shouldn't break the main flow
    }
  }

  private prepareTextForEmbedding(content: string, additionalContext?: string): string {
    // Clean and prepare text for embedding
    let text = content.trim();
    
    // Remove excessive whitespace
    text = text.replace(/\s+/g, ' ');
    
    // Add additional context if provided
    if (additionalContext) {
      text = `${additionalContext}\n\n${text}`;
    }
    
    // Truncate if too long (adjust based on model limits)
    const maxLength = 8000; // Conservative limit for most embedding models
    if (text.length > maxLength) {
      text = text.substring(0, maxLength) + '...';
    }
    
    return text;
  }

  private async generateOpenAIEmbedding(text: string): Promise<EmbeddingResult> {
    const { OpenAI } = require('openai');

    const openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });

    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      const tokensUsed = response.usage.total_tokens;
      const costPer1kTokens = 0.0001; // OpenAI ada-002 pricing
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        vector: response.data[0].embedding,
        tokensUsed,
        model: 'text-embedding-ada-002',
        provider: 'openai',
        cost
      };

    } catch (error) {
      this.logger.error('OpenAI embedding error:', error);
      throw error;
    }
  }

  private async generateGeminiEmbedding(text: string): Promise<EmbeddingResult> {
    const { GoogleGenerativeAI } = require('@google/generative-ai');

    const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));

    try {
      // Use Gemini's text embedding model
      const model = genAI.getGenerativeModel({ model: 'gemini-embedding-001' });

      const result = await model.embedContent(text);

      const tokensUsed = Math.round(text.split(' ').length); // Rough estimation, rounded to integer
      const costPer1kTokens = 0.0000125; // Gemini embedding pricing estimate
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        vector: result.embedding.values,
        tokensUsed,
        model: 'gemini-embedding-001',
        provider: 'gemini',
        cost
      };

    } catch (error) {
      this.logger.error('Gemini embedding error:', error);

      // Fallback to a simple hash-based vector (not recommended for production)
      return this.generateFallbackEmbedding(text);
    }
  }

  private generateFallbackEmbedding(text: string): EmbeddingResult {
    // Simple fallback - creates a basic vector representation
    // This is NOT suitable for production use
    const words = text.toLowerCase().split(/\s+/);
    const vector = new Array(1536).fill(0); // Match OpenAI dimensions
    
    // Simple hash-based approach
    words.forEach((word) => {
      const hash = this.simpleHash(word);
      vector[hash % 1536] += 1;
    });
    
    // Normalize vector
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude > 0) {
      for (let i = 0; i < vector.length; i++) {
        vector[i] /= magnitude;
      }
    }
    
    return {
      vector,
      tokensUsed: words.length,
      model: 'fallback-hash',
      provider: 'local'
    };
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private async recordTokenUsage(
    requirementId: string,
    tokensUsed: number,
    model: string,
    provider: string
  ): Promise<void> {
    if (tokensUsed > 0) {
      const tokenUsage = this.tokenUsageRepository.create({
        requirementId,
        tokenType: TokenType.EMBEDDING,
        tokensUsed,
        model,
        provider,
        cost: this.calculateCost(tokensUsed, provider),
        metadata: {
          operation: 'embedding_generation',
          timestamp: new Date()
        }
      });

      await this.tokenUsageRepository.save(tokenUsage);
    }
  }

  private calculateCost(tokens: number, provider: string): number {
    // Cost calculation based on provider
    const costPerToken = provider === 'openai' ? 0.0000001 : 0.00000005; // USD per token for embeddings
    return tokens * costPerToken;
  }

  async getEmbeddingStatus(requirementId: string): Promise<EmbeddingStatus> {
    const requirement = await this.requirementRepository.findOne({
      where: { id: requirementId },
      select: ['metadata']
    });

    return requirement?.metadata?.embeddingStatus || EmbeddingStatus.NOT_STARTED;
  }
}
