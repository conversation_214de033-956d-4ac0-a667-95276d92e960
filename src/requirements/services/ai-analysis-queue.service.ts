import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, Job } from 'bullmq';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, AiAnalysis } from '../../entities';

export interface AiAnalysisJobData {
  requirementId: string;
  userCredentials: {
    userId: string;
    companyId: string;
    projectId: string;
  };
  jobId: string;
}

export interface AiAnalysisJobResult {
  requirementId: string;
  success: boolean;
  analysisGenerated: boolean;
  qualityScore?: number;
  tokensUsed: number;
  message: string;
  error?: string;
}

export enum AiAnalysisStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

@Injectable()
export class AiAnalysisQueueService {
  private readonly logger = new Logger(AiAnalysisQueueService.name);

  constructor(
    @InjectQueue('ai-analysis') private aiAnalysisQueue: Queue<AiAnalysisJobData, AiAnalysisJobResult>,
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(AiAnalysis)
    private aiAnalysisRepository: Repository<AiAnalysis>,
  ) {}

  async addAiAnalysisJob(
    requirementId: string,
    userCredentials: { userId: string; companyId: string; projectId: string }
  ): Promise<string> {
    const jobId = `ai_analysis_${uuidv4()}`;

    // Mark analysis as in progress
    await this.updateAnalysisStatus(requirementId, AiAnalysisStatus.IN_PROGRESS, jobId);

    const jobData: AiAnalysisJobData = {
      requirementId,
      userCredentials,
      jobId
    };

    const job = await this.aiAnalysisQueue.add('generate-ai-analysis', jobData, {
      jobId,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 5000,
      },
      removeOnComplete: 100, // Keep last 100 completed jobs
      removeOnFail: 50,      // Keep last 50 failed jobs
    });

    this.logger.log(`AI analysis job ${jobId} added to queue for requirement ${requirementId}`);
    return jobId;
  }

  async getAiAnalysisJobStatus(jobId: string): Promise<any> {
    try {
      const job = await this.aiAnalysisQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      const state = await job.getState();
      const progress = job.progress || 0;

      return {
        jobId: job.id,
        status: state,
        progress,
        createdAt: new Date(job.timestamp),
        completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        error: job.failedReason || undefined,
        result: state === 'completed' ? job.returnvalue : undefined
      };
    } catch (error) {
      this.logger.error(`Error getting AI analysis job status for ${jobId}:`, error);
      return null;
    }
  }

  async getRequirementAnalysisStatus(requirementId: string): Promise<{
    status: AiAnalysisStatus;
    jobId?: string;
    progress?: number;
    error?: string;
    analysis?: any;
  }> {
    try {
      // Get requirement metadata separately to avoid relation issues
      const requirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['id', 'metadata']
      });

      if (!requirement) {
        return { status: AiAnalysisStatus.NOT_STARTED };
      }

      const metadata = requirement.metadata || {};
      const status = metadata.aiAnalysisStatus || AiAnalysisStatus.NOT_STARTED;
      const jobId = metadata.aiAnalysisJobId;

      // If job is in progress, get current status from queue
      if (status === AiAnalysisStatus.IN_PROGRESS && jobId) {
        const jobStatus = await this.getAiAnalysisJobStatus(jobId);
        if (jobStatus) {
          return {
            status: this.mapJobStateToAnalysisStatus(jobStatus.status),
            jobId,
            progress: jobStatus.progress,
            error: jobStatus.error
          };
        }
      }

      // Get latest analysis separately to avoid relation query issues
      let latestAnalysis = null;
      try {
        latestAnalysis = await this.aiAnalysisRepository.findOne({
          where: { requirementId },
          order: { analysisDate: 'DESC' }
        });
      } catch (analysisError) {
        this.logger.warn(`Could not fetch analysis for requirement ${requirementId}:`, analysisError);
        // Continue without analysis data
      }

      return {
        status,
        jobId,
        error: metadata.aiAnalysisError,
        analysis: latestAnalysis ? {
          id: latestAnalysis.id,
          aiQualityScore: latestAnalysis.aiQualityScore,
          aiFeedback: latestAnalysis.aiFeedback,
          analysisDate: latestAnalysis.analysisDate.toISOString()
        } : undefined
      };

    } catch (error) {
      this.logger.error(`Error getting requirement analysis status for ${requirementId}:`, error);
      return { status: AiAnalysisStatus.FAILED, error: error.message };
    }
  }

  async retryAnalysisJob(requirementId: string): Promise<string | null> {
    try {
      const requirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      if (!requirement) {
        throw new Error('Requirement not found');
      }

      const metadata = requirement.metadata || {};
      const userCredentials = metadata.processedBy || {
        userId: 'system',
        companyId: 'system',
        projectId: 'system'
      };

      // Create new analysis job
      const jobId = await this.addAiAnalysisJob(requirementId, userCredentials);

      this.logger.log(`Retrying AI analysis for requirement ${requirementId} with job ${jobId}`);
      return jobId;

    } catch (error) {
      this.logger.error(`Error retrying AI analysis for requirement ${requirementId}:`, error);
      return null;
    }
  }

  async cancelAnalysisJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.aiAnalysisQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.remove();
      
      // Update requirement status
      const jobData = job.data;
      if (jobData?.requirementId) {
        await this.updateAnalysisStatus(
          jobData.requirementId, 
          AiAnalysisStatus.FAILED, 
          undefined, 
          'Job cancelled by user'
        );
      }

      this.logger.log(`AI analysis job ${jobId} cancelled`);
      return true;
    } catch (error) {
      this.logger.error(`Error cancelling AI analysis job ${jobId}:`, error);
      return false;
    }
  }

  async getQueueStats() {
    const waiting = await this.aiAnalysisQueue.getWaiting();
    const active = await this.aiAnalysisQueue.getActive();
    const completed = await this.aiAnalysisQueue.getCompleted();
    const failed = await this.aiAnalysisQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  }

  private async updateAnalysisStatus(
    requirementId: string,
    status: AiAnalysisStatus,
    jobId?: string,
    error?: string
  ): Promise<void> {
    try {
      // Get current metadata to preserve existing data
      const currentRequirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      const currentMetadata = currentRequirement?.metadata || {};

      const updatedMetadata: Record<string, any> = {
        ...currentMetadata,
        aiAnalysisStatus: status,
        aiAnalysisTimestamp: new Date().toISOString(),
      };

      if (jobId) {
        updatedMetadata['aiAnalysisJobId'] = jobId;
      }

      if (error) {
        updatedMetadata['aiAnalysisError'] = error;
      }

      await this.requirementRepository.update(requirementId, {
        metadata: updatedMetadata as any
      });
    } catch (error) {
      this.logger.error(`Error updating analysis status for requirement ${requirementId}:`, error);
    }
  }

  private mapJobStateToAnalysisStatus(jobState: string): AiAnalysisStatus {
    switch (jobState) {
      case 'waiting':
      case 'delayed':
      case 'active':
        return AiAnalysisStatus.IN_PROGRESS;
      case 'completed':
        return AiAnalysisStatus.COMPLETED;
      case 'failed':
        return AiAnalysisStatus.FAILED;
      default:
        return AiAnalysisStatus.NOT_STARTED;
    }
  }
}
