import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, Job } from 'bullmq';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, EmbeddingStatus } from '../../entities';

export interface EmbeddingJobData {
  requirementId: string;
  content: string;
  additionalContext?: string;
  jobId: string;
}

export interface FileEmbeddingJobData {
  requirementId: string;
  storageUrl: string;
  userCredentials: {
    userId: string;
    companyId: string;
    projectId: string;
  };
  jobId: string;
}

export interface EmbeddingJobResult {
  requirementId: string;
  success: boolean;
  embeddingGenerated: boolean;
  tokensUsed: number;
  message: string;
  error?: string;
}

@Injectable()
export class EmbeddingQueueService {
  private readonly logger = new Logger(EmbeddingQueueService.name);

  constructor(
    @InjectQueue('embeddings') private embeddingQueue: Queue<EmbeddingJobData | FileEmbeddingJobData, EmbeddingJobResult>,
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
  ) {}

  async addEmbeddingJob(
    requirementId: string,
    content: string,
    additionalContext?: string
  ): Promise<string> {
    const jobId = `embedding_${uuidv4()}`;

    // Mark embedding as in progress
    await this.updateEmbeddingStatus(requirementId, EmbeddingStatus.IN_PROGRESS, jobId);

    const jobData: EmbeddingJobData = {
      requirementId,
      content,
      additionalContext,
      jobId
    };

    const job = await this.embeddingQueue.add('generate-embedding', jobData, {
      jobId,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 5000,
      },
      removeOnComplete: 100, // Keep last 100 completed jobs
      removeOnFail: 50,      // Keep last 50 failed jobs
    });

    this.logger.log(`Embedding job ${jobId} added to queue for requirement ${requirementId}`);
    return jobId;
  }

  async addFileEmbeddingJob(
    requirementId: string,
    storageUrl: string,
    userCredentials: { userId: string; companyId: string; projectId: string }
  ): Promise<string> {
    const jobId = `file_embedding_${uuidv4()}`;

    // Mark embedding as in progress
    await this.updateEmbeddingStatus(requirementId, EmbeddingStatus.IN_PROGRESS, jobId);

    const jobData: FileEmbeddingJobData = {
      requirementId,
      storageUrl,
      userCredentials,
      jobId
    };

    const job = await this.embeddingQueue.add('generate-file-embedding', jobData, {
      jobId,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 5000,
      },
      removeOnComplete: 100, // Keep last 100 completed jobs
      removeOnFail: 50,      // Keep last 50 failed jobs
    });

    this.logger.log(`File embedding job ${jobId} added to queue for requirement ${requirementId}`);
    return jobId;
  }

  async getEmbeddingJobStatus(jobId: string): Promise<any> {
    try {
      const job = await this.embeddingQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      const state = await job.getState();
      const progress = job.progress || 0;

      return {
        jobId: job.id,
        status: state,
        progress,
        createdAt: new Date(job.timestamp),
        completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        error: job.failedReason || undefined,
        result: state === 'completed' ? job.returnvalue : undefined
      };
    } catch (error) {
      this.logger.error(`Error getting embedding job status for ${jobId}:`, error);
      return null;
    }
  }

  async getRequirementEmbeddingStatus(requirementId: string): Promise<{
    status: EmbeddingStatus;
    jobId?: string;
    progress?: number;
    error?: string;
  }> {
    try {
      const requirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      if (!requirement) {
        return { status: EmbeddingStatus.NOT_STARTED };
      }

      const metadata = requirement.metadata || {};
      const status = metadata.embeddingStatus || EmbeddingStatus.NOT_STARTED;
      const jobId = metadata.embeddingJobId;

      // If job is in progress, get current status from queue
      if (status === EmbeddingStatus.IN_PROGRESS && jobId) {
        const jobStatus = await this.getEmbeddingJobStatus(jobId);
        if (jobStatus) {
          return {
            status: this.mapJobStateToEmbeddingStatus(jobStatus.status),
            jobId,
            progress: jobStatus.progress,
            error: jobStatus.error
          };
        }
      }

      return {
        status,
        jobId,
        error: metadata.errorDetails
      };

    } catch (error) {
      this.logger.error(`Error getting requirement embedding status for ${requirementId}:`, error);
      return { status: EmbeddingStatus.FAILED, error: error.message };
    }
  }

  async retryEmbeddingJob(requirementId: string): Promise<string | null> {
    try {
      const requirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['content', 'metadata']
      });

      if (!requirement) {
        throw new Error('Requirement not found');
      }

      // Create new embedding job
      const jobId = await this.addEmbeddingJob(
        requirementId,
        requirement.content,
        requirement.metadata?.fileInfo?.originalMimeType
      );

      this.logger.log(`Retrying embedding for requirement ${requirementId} with job ${jobId}`);
      return jobId;

    } catch (error) {
      this.logger.error(`Error retrying embedding for requirement ${requirementId}:`, error);
      return null;
    }
  }

  async cancelEmbeddingJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.embeddingQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.remove();
      
      // Update requirement status
      const jobData = job.data;
      if (jobData?.requirementId) {
        await this.updateEmbeddingStatus(
          jobData.requirementId, 
          EmbeddingStatus.FAILED, 
          undefined, 
          'Job cancelled by user'
        );
      }

      this.logger.log(`Embedding job ${jobId} cancelled`);
      return true;
    } catch (error) {
      this.logger.error(`Error cancelling embedding job ${jobId}:`, error);
      return false;
    }
  }

  async getQueueStats() {
    const waiting = await this.embeddingQueue.getWaiting();
    const active = await this.embeddingQueue.getActive();
    const completed = await this.embeddingQueue.getCompleted();
    const failed = await this.embeddingQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  }

  private async updateEmbeddingStatus(
    requirementId: string,
    status: EmbeddingStatus,
    jobId?: string,
    error?: string
  ): Promise<void> {
    try {
      // Get current metadata to preserve existing data
      const currentRequirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      const currentMetadata = currentRequirement?.metadata || {};

      const updatedMetadata: Record<string, any> = {
        ...currentMetadata,
        embeddingStatus: status,
        processingTimestamp: new Date().toISOString(),
      };

      if (jobId) {
        updatedMetadata['embeddingJobId'] = jobId;
      }

      if (error) {
        updatedMetadata['errorDetails'] = error;
      }

      await this.requirementRepository.update(requirementId, {
        metadata: updatedMetadata as any
      });
    } catch (error) {
      this.logger.error(`Error updating embedding status for requirement ${requirementId}:`, error);
    }
  }

  private mapJobStateToEmbeddingStatus(jobState: string): EmbeddingStatus {
    switch (jobState) {
      case 'waiting':
      case 'delayed':
      case 'active':
        return EmbeddingStatus.IN_PROGRESS;
      case 'completed':
        return EmbeddingStatus.COMPLETED;
      case 'failed':
        return EmbeddingStatus.FAILED;
      default:
        return EmbeddingStatus.NOT_STARTED;
    }
  }
}
