import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, ProcessingMethod, EmbeddingStatus } from '../../entities';
import { LLMValidationService } from './llm-validation.service';
import { EmbeddingQueueService } from './embedding-queue.service';

export interface HybridProcessingResult {
  content: string;
  processingMethod: ProcessingMethod;
  requiresEmbedding: boolean;
  embeddingJobId?: string;
  metadata: Record<string, any>;
}

@Injectable()
export class HybridFileProcessorService {
  private readonly logger = new Logger(HybridFileProcessorService.name);

  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    private llmValidationService: LLMValidationService,
    private embeddingQueueService: EmbeddingQueueService,
  ) {}

  async processFile(
    fileBuffer: <PERSON>uffer,
    originalFilename: string,
    mimetype: string,
    requirementId: string
  ): Promise<HybridProcessingResult> {
    this.logger.log(`Starting hybrid processing for file ${originalFilename} (${mimetype})`);

    try {
      // Step 1: Extract basic content using traditional parsing
      const basicContent = await this.extractBasicContent(fileBuffer, mimetype, originalFilename);
      
      // Step 2: Use LLM to validate if traditional parsing is sufficient
      const validationResult = await this.llmValidationService.validateFileForTraditionalParsing(
        fileBuffer,
        mimetype,
        originalFilename,
        requirementId
      );

      // Step 3: Determine processing method and next steps
      const processingMethod = this.determineProcessingMethod(validationResult, mimetype);
      const requiresEmbedding = !validationResult.canUseTraditionalParse || processingMethod === ProcessingMethod.EMBEDDING_BASED;

      // Step 4: Prepare metadata
      const metadata: Record<string, any> = {
        processingMethod,
        embeddingStatus: requiresEmbedding ? EmbeddingStatus.NOT_STARTED : EmbeddingStatus.COMPLETED,
        hasImages: validationResult.hasImages,
        hasDiagrams: validationResult.hasDiagrams,
        hasComplexLayout: validationResult.hasComplexLayout,
        llmValidationResult: validationResult,
        fileInfo: {
          originalMimeType: mimetype,
          fileSize: fileBuffer.length,
          originalFilename,
        },
        processingTimestamp: new Date(),
      };

      // Step 5: Start embedding process if needed
      let embeddingJobId: string | undefined;
      if (requiresEmbedding) {
        this.logger.log(`File ${originalFilename} requires embedding processing`);

        // Prepare content for embedding (might include additional context)
        const embeddingContent = this.prepareContentForEmbedding(
          basicContent,
          validationResult,
          mimetype
        );

        embeddingJobId = await this.embeddingQueueService.addEmbeddingJob(
          requirementId,
          embeddingContent,
          `File: ${originalFilename} (${mimetype})`
        );

        metadata.embeddingJobId = embeddingJobId;
        metadata.embeddingStatus = EmbeddingStatus.IN_PROGRESS;
      }

      const result: HybridProcessingResult = {
        content: basicContent,
        processingMethod,
        requiresEmbedding,
        embeddingJobId,
        metadata
      };

      this.logger.log(`Hybrid processing completed for ${originalFilename}. Method: ${processingMethod}, Requires embedding: ${requiresEmbedding}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error in hybrid processing for file ${originalFilename}:`, error);
      
      // Fallback to traditional processing
      const fallbackContent = await this.extractBasicContent(fileBuffer, mimetype, originalFilename);
      
      return {
        content: fallbackContent,
        processingMethod: ProcessingMethod.TRADITIONAL_PARSE,
        requiresEmbedding: false,
        metadata: {
          processingMethod: ProcessingMethod.TRADITIONAL_PARSE,
          embeddingStatus: EmbeddingStatus.COMPLETED,
          fileInfo: {
            originalMimeType: mimetype,
            fileSize: fileBuffer.length,
            originalFilename,
          },
          processingTimestamp: new Date(),
        } as Record<string, any>
      };
    }
  }

  private async extractBasicContent(
    fileBuffer: Buffer,
    mimetype: string,
    originalFilename: string
  ): Promise<string> {
    try {
      if (mimetype === 'application/pdf') {
        const pdfParse = require('pdf-parse');
        const data = await pdfParse(fileBuffer);
        return data.text;
      } 
      
      if (mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const mammoth = require('mammoth');
        const result = await mammoth.extractRawText({ buffer: fileBuffer });
        return result.value;
      } 
      
      if (mimetype.startsWith('text/')) {
        return fileBuffer.toString('utf-8');
      }

      if (mimetype === 'application/vnd.ms-excel' || 
          mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        const XLSX = require('xlsx');
        const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
        let content = '';
        
        workbook.SheetNames.forEach(sheetName => {
          const sheet = workbook.Sheets[sheetName];
          content += `Sheet: ${sheetName}\n`;
          content += XLSX.utils.sheet_to_txt(sheet);
          content += '\n\n';
        });
        
        return content;
      }

      // For other file types, return a placeholder
      return `[Content extracted from ${originalFilename}]\n\nFile type: ${mimetype}\nSize: ${fileBuffer.length} bytes\n\nThis file may contain visual elements or complex formatting that requires advanced processing.`;

    } catch (error) {
      this.logger.error(`Error extracting basic content from ${originalFilename}:`, error);
      return `[Error extracting content from ${originalFilename}]\n\nFile type: ${mimetype}\nError: ${error.message}`;
    }
  }

  private determineProcessingMethod(
    validationResult: any,
    mimetype: string
  ): ProcessingMethod {
    // If LLM says traditional parsing is sufficient and confidence is high
    if (validationResult.canUseTraditionalParse && validationResult.confidence > 0.8) {
      return ProcessingMethod.TRADITIONAL_PARSE;
    }

    // If file has complex visual elements, use embedding-based processing
    if (validationResult.hasImages || validationResult.hasDiagrams || validationResult.hasComplexLayout) {
      return ProcessingMethod.EMBEDDING_BASED;
    }

    // For PDFs and complex documents, use hybrid approach
    if (mimetype === 'application/pdf' || 
        mimetype.includes('wordprocessingml') ||
        mimetype.includes('presentationml')) {
      return ProcessingMethod.HYBRID;
    }

    // Default to traditional for simple text files
    return ProcessingMethod.TRADITIONAL_PARSE;
  }

  private prepareContentForEmbedding(
    basicContent: string,
    validationResult: any,
    mimetype: string
  ): string {
    let embeddingContent = basicContent;

    // Add context about the file type and detected elements
    const contextInfo = [
      `File type: ${mimetype}`,
      `Processing method: Advanced embedding-based extraction`,
    ];

    if (validationResult.hasImages) {
      contextInfo.push('Contains images or visual elements');
    }

    if (validationResult.hasDiagrams) {
      contextInfo.push('Contains diagrams or charts');
    }

    if (validationResult.hasComplexLayout) {
      contextInfo.push('Has complex layout or formatting');
    }

    if (validationResult.detectedElements?.length > 0) {
      contextInfo.push(`Detected elements: ${validationResult.detectedElements.join(', ')}`);
    }

    const context = `[Document Context]\n${contextInfo.join('\n')}\n\n[Content]\n`;
    
    return context + embeddingContent;
  }

  async getProcessingStatus(requirementId: string): Promise<{
    processingMethod?: ProcessingMethod;
    embeddingStatus?: EmbeddingStatus;
    embeddingJobId?: string;
    progress?: number;
    error?: string;
  }> {
    try {
      const requirement = await this.requirementRepository.findOne({
        where: { id: requirementId },
        select: ['metadata']
      });

      if (!requirement?.metadata) {
        return {};
      }

      const metadata = requirement.metadata;
      
      // If embedding is in progress, get current status
      if (metadata.embeddingStatus === EmbeddingStatus.IN_PROGRESS && metadata.embeddingJobId) {
        const embeddingStatus = await this.embeddingQueueService.getRequirementEmbeddingStatus(requirementId);
        
        return {
          processingMethod: metadata.processingMethod,
          embeddingStatus: embeddingStatus.status,
          embeddingJobId: metadata.embeddingJobId,
          progress: embeddingStatus.progress,
          error: embeddingStatus.error
        };
      }

      return {
        processingMethod: metadata.processingMethod,
        embeddingStatus: metadata.embeddingStatus,
        embeddingJobId: metadata.embeddingJobId,
        error: metadata.errorDetails
      };

    } catch (error) {
      this.logger.error(`Error getting processing status for requirement ${requirementId}:`, error);
      return { error: error.message };
    }
  }
}
