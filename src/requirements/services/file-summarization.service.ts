import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TokenUsage, TokenType } from '../../entities';

interface SummarizationResult {
  summary: string;
  tokensUsed: number;
  model: string;
  provider: string;
  cost?: number;
  hasImages: boolean;
  hasDiagrams: boolean;
  hasComplexLayout: boolean;
  detectedElements: string[];
}

@Injectable()
export class FileSummarizationService {
  private readonly logger = new Logger(FileSummarizationService.name);
  private readonly llmProvider: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    this.llmProvider = this.configService.get<string>('LLM', 'OPENAI');
  }

  async summarizeFile(
    fileBuffer: Buffer,
    mimetype: string,
    originalFilename: string,
    requirementId: string
  ): Promise<SummarizationResult> {
    try {
      this.logger.log(`Starting file summarization for ${originalFilename} using ${this.llmProvider}`);

      // Extract basic text content first
      const extractedText = this.extractTextFromFile(fileBuffer, mimetype, originalFilename);

      // Generate summary using LLM
      const result = await this.generateSummaryWithLLM(
        extractedText,
        mimetype,
        originalFilename
      );

      // Track token usage
      await this.trackTokenUsage(
        requirementId,
        'content_extraction',
        result.tokensUsed,
        result.model,
        result.provider,
        result.cost
      );

      this.logger.log(`File summarization completed for ${originalFilename}. Tokens used: ${result.tokensUsed}`);
      return result;

    } catch (error) {
      this.logger.error(`Error summarizing file ${originalFilename}:`, error);
      
      // Return fallback summary
      return this.generateFallbackSummary(fileBuffer, mimetype, originalFilename);
    }
  }

  private async generateSummaryWithLLM(
    extractedText: string,
    mimetype: string,
    filename: string
  ): Promise<SummarizationResult> {
    const prompt = this.buildSummarizationPrompt(extractedText, mimetype, filename);

    if (this.llmProvider.toUpperCase() === 'OPENAI') {
      return this.generateOpenAISummary(prompt);
    } else if (this.llmProvider.toUpperCase() === 'GEMINI') {
      return this.generateGeminiSummary(prompt);
    } else {
      throw new Error(`Unsupported LLM provider: ${this.llmProvider}`);
    }
  }

  private buildSummarizationPrompt(text: string, mimetype: string, filename: string): string {
    return `
You are an expert document analyzer. Please analyze the following document content and provide a comprehensive summary.

Document Information:
- Filename: ${filename}
- File Type: ${mimetype}
- Content Length: ${text.length} characters

Instructions:
1. Provide a clear, concise summary of the document content (2-3 paragraphs)
2. Identify key topics, requirements, or main points
3. Analyze the document structure and visual elements
4. Determine if the document contains:
   - Images or diagrams
   - Complex layouts (tables, multi-column, etc.)
   - Technical diagrams or charts

Document Content:
${text.substring(0, 6000)} ${text.length > 6000 ? '...(truncated)' : ''}

Please respond in the following JSON format:
{
  "summary": "Detailed summary of the document content...",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "hasImages": true/false,
  "hasDiagrams": true/false,
  "hasComplexLayout": true/false,
  "detectedElements": ["images", "tables", "diagrams", "charts", "code"],
  "documentType": "requirements/specification/manual/other",
  "confidence": 0.95
}
`;
  }

  private async generateOpenAISummary(prompt: string): Promise<SummarizationResult> {
    const { OpenAI } = require('openai');

    const openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });

    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert document analyzer. Respond only with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      });

      const content = response.choices[0].message.content;
      const analysis = JSON.parse(content);

      const tokensUsed = response.usage.total_tokens;
      const costPer1kTokens = 0.03; // GPT-4 pricing
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        summary: analysis.summary,
        tokensUsed,
        model: 'gpt-4',
        provider: 'openai',
        cost,
        hasImages: analysis.hasImages || false,
        hasDiagrams: analysis.hasDiagrams || false,
        hasComplexLayout: analysis.hasComplexLayout || false,
        detectedElements: analysis.detectedElements || []
      };

    } catch (error) {
      this.logger.error('OpenAI summarization error:', error);
      throw error;
    }
  }

  private async generateGeminiSummary(prompt: string): Promise<SummarizationResult> {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    
    const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));
    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });

    try {
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const content = response.text();

      // Clean the response - remove markdown code blocks if present
      const cleanContent = content.replace(/```json\s*|\s*```/g, '').trim();
      const analysis = JSON.parse(cleanContent);
      
      const tokensUsed = Math.round(content.length / 4); // Rough estimation, rounded to integer
      const costPer1kTokens = 0.000125; // Gemini Pro pricing estimate
      const cost = (tokensUsed / 1000) * costPer1kTokens;

      return {
        summary: analysis.summary,
        tokensUsed,
        model: 'gemini-2.0-flash',
        provider: 'gemini',
        cost,
        hasImages: analysis.hasImages || false,
        hasDiagrams: analysis.hasDiagrams || false,
        hasComplexLayout: analysis.hasComplexLayout || false,
        detectedElements: analysis.detectedElements || []
      };

    } catch (error) {
      this.logger.error('Gemini summarization error:', error);
      throw error;
    }
  }

  private extractTextFromFile(buffer: Buffer, mimetype: string, originalname: string): string {
    // Basic text extraction - in production, use proper libraries
    if (mimetype.includes('text/') || originalname.endsWith('.md') || originalname.endsWith('.txt')) {
      return buffer.toString('utf-8');
    }
    
    // For other file types, return a placeholder that includes file info
    return `[File: ${originalname}]
File Type: ${mimetype}
File Size: ${buffer.length} bytes

This file requires specialized parsing for ${mimetype} format. 
The content would be extracted using appropriate libraries such as:
- PDF files: pdf-parse, pdf2pic for images
- DOCX files: mammoth, docx-parser
- Images: OCR with tesseract.js
- Excel: xlsx, exceljs

File content extraction and analysis would be performed here.`;
  }

  private generateFallbackSummary(
    fileBuffer: Buffer,
    mimetype: string,
    filename: string
  ): SummarizationResult {
    const basicText = this.extractTextFromFile(fileBuffer, mimetype, filename);
    
    return {
      summary: `Document analysis for ${filename} (${mimetype}). File size: ${fileBuffer.length} bytes. ${basicText.substring(0, 200)}...`,
      tokensUsed: 0,
      model: 'fallback',
      provider: 'local',
      cost: 0,
      hasImages: mimetype.includes('image') || mimetype.includes('pdf'),
      hasDiagrams: false,
      hasComplexLayout: mimetype.includes('pdf') || mimetype.includes('docx'),
      detectedElements: [mimetype.split('/')[0]]
    };
  }

  private async trackTokenUsage(
    requirementId: string,
    tokenType: string,
    tokensUsed: number,
    model: string,
    provider: string,
    cost?: number
  ): Promise<void> {
    try {
      const tokenUsage = this.tokenUsageRepository.create({
        requirementId,
        tokenType: tokenType as TokenType,
        tokensUsed,
        model,
        provider,
        cost,
        metadata: {
          timestamp: new Date().toISOString(),
          operation: 'file_summarization',
          model,
          provider
        }
      });

      await this.tokenUsageRepository.save(tokenUsage);
      this.logger.log(`Token usage tracked: ${tokensUsed} tokens for ${tokenType} using ${model}`);

    } catch (error) {
      this.logger.error(`Error tracking token usage for requirement ${requirementId}:`, error);
    }
  }
}
