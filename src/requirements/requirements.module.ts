import { Module, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { RequirementsController } from './requirements.controller';
import { RequirementsService } from './requirements.service';
import { RequirementStatusSeederService } from './requirement-status-seeder.service';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import { DatabaseTestService } from './database-test.service';
import { UploadQueueService } from './services/upload-queue.service';
import { BulkDeleteQueueService } from './services/bulk-delete-queue.service';
import { UploadProcessor } from './processors/upload.processor';
import { BulkDeleteProcessor } from './processors/bulk-delete.processor';
import { EmbeddingProcessor } from './processors/embedding.processor';
import { AiAnalysisProcessor } from './processors/ai-analysis.processor';
import { LLMValidationService } from './services/llm-validation.service';
import { EmbeddingService } from './services/embedding.service';
import { EmbeddingQueueService } from './services/embedding-queue.service';
import { HybridFileProcessorService } from './services/hybrid-file-processor.service';
import { FileSummarizationService } from './services/file-summarization.service';
import { AiAnalysisService } from './services/ai-analysis.service';
import { AiAnalysisQueueService } from './services/ai-analysis-queue.service';
import { UploadProgressGateway } from './gateways/upload-progress.gateway';
import {
  User,
  RequirementStatus,
  Requirement,
  RequirementRevision,
  AiAnalysis,
  TokenUsage
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      RequirementStatus,
      Requirement,
      RequirementRevision,
      AiAnalysis,
      TokenUsage,
    ]),
    BullModule.registerQueue({
      name: 'upload-queue',
    }),
    BullModule.registerQueue({
      name: 'bulk-delete-queue',
    }),
    BullModule.registerQueue({
      name: 'embeddings',
    }),
    BullModule.registerQueue({
      name: 'ai-analysis',
    }),
  ],
  controllers: [RequirementsController],
  providers: [
    RequirementsService,
    RequirementStatusSeederService,
    GoogleCloudStorageService,
    DatabaseTestService,
    UploadQueueService,
    BulkDeleteQueueService,
    EmbeddingQueueService,
    LLMValidationService,
    EmbeddingService,
    HybridFileProcessorService,
    FileSummarizationService,
    AiAnalysisService,
    AiAnalysisQueueService,
    UploadProcessor,
    BulkDeleteProcessor,
    EmbeddingProcessor,
    AiAnalysisProcessor,
    UploadProgressGateway,
    {
      provide: 'UploadProgressGateway',
      useExisting: UploadProgressGateway,
    },
  ],
  exports: [RequirementsService, UploadQueueService, BulkDeleteQueueService, EmbeddingQueueService, EmbeddingService],
})
export class RequirementsModule implements OnModuleInit {
  constructor(private readonly seederService: RequirementStatusSeederService) {}

  async onModuleInit() {
    await this.seederService.seed();
  }
}
