# Testing AI Quality Score in Requirements List

## Test the Updated GET /api/v1/requirements Endpoint

### 1. Test Requirements List with AI Quality Score

```bash
curl -X GET "http://localhost:3002/api/v1/requirements?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response Format:**
```json
{
  "data": [
    {
      "id": "364b9413-73f9-4099-a459-b0ec935e5346",
      "requirement_id": "R-001",
      "name": "User Authentication Requirements",
      "status": "reviewed_by_ai",
      "storage_url": "https://storage.googleapis.com/...",
      "uploaded_by": "user-uuid",
      "created_at": "2025-01-15T10:00:00Z",
      "updated_at": "2025-01-15T10:30:00Z",
      "ai_quality_score": 78
    },
    {
      "id": "another-requirement-id",
      "requirement_id": "R-002", 
      "name": "Payment Processing Requirements",
      "status": "draft",
      "storage_url": "https://storage.googleapis.com/...",
      "uploaded_by": "user-uuid",
      "created_at": "2025-01-15T11:00:00Z",
      "updated_at": "2025-01-15T11:00:00Z",
      "ai_quality_score": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 2,
    "totalPages": 1
  }
}
```

### 2. Test Single Requirement with AI Quality Score

```bash
curl -X GET "http://localhost:3002/api/v1/requirements/364b9413-73f9-4099-a459-b0ec935e5346" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response Format:**
```json
{
  "id": "364b9413-73f9-4099-a459-b0ec935e5346",
  "requirement_id": "R-001",
  "name": "User Authentication Requirements",
  "content": "# User Authentication\n\nThe system shall...",
  "status": "reviewed_by_ai",
  "storage_url": "https://storage.googleapis.com/...",
  "uploaded_by": "user-uuid",
  "created_at": "2025-01-15T10:00:00Z",
  "updated_at": "2025-01-15T10:30:00Z",
  "ai_quality_score": 78
}
```

### 3. Test Filtering by Status

```bash
curl -X GET "http://localhost:3002/api/v1/requirements?status=reviewed_by_ai&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

This should return only requirements with `reviewed_by_ai` status, each including their AI quality score.

## Expected Behavior

### For Requirements with AI Analysis:
- ✅ `ai_quality_score`: Integer value between 1-100
- ✅ Shows the score from the **latest** AI analysis

### For Requirements without AI Analysis:
- ✅ `ai_quality_score`: `null`
- ✅ No errors or missing fields

### Performance Considerations:
- ✅ Uses efficient subquery to get latest analysis per requirement
- ✅ Single database query for all AI scores (no N+1 problem)
- ✅ Graceful error handling if AI analysis query fails

## Database Query Verification

You can verify the underlying query works correctly:

```sql
-- Check latest AI analysis per requirement
SELECT 
  r.id as requirement_id,
  r.name,
  a.ai_quality_score,
  a.analysis_date
FROM requirements r
LEFT JOIN ai_analyses a ON r.id = a.requirement_id
WHERE a.analysis_date = (
  SELECT MAX(a2.analysis_date) 
  FROM ai_analyses a2 
  WHERE a2.requirement_id = r.id
)
OR a.id IS NULL
ORDER BY r.created_at DESC;
```

## API Documentation Update

The Swagger documentation will now show:
- `ai_quality_score` field in both list and detail responses
- Proper type annotation (number, nullable)
- Min/max values (1-100)
- Clear description of what the score represents
