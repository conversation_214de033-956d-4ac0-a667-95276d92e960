-- Database schema for Requirements Management System
-- This file contains the SQL DDL statements to create the database structure

-- Create users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    company_id UUID NOT NULL,
    project_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lookup table for requirement statuses
-- Using a separate table ensures data consistency and makes it easy to add new statuses.
CREATE TABLE requirement_statuses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL, -- e.g., 'draft', 'reviewed_by_ai', 'approved', 'published'
    description TEXT
);

-- Initial data for requirement_statuses
INSERT INTO requirement_statuses (name, description) VALUES
('draft', 'The requirement has been uploaded but not yet processed.'),
('reviewed_by_ai', 'AI analysis is complete and awaiting user review.'),
('revised', 'The requirement has been modified by the user after AI review.'),
('approved', 'The user has approved the requirement.'),
('published', 'The requirement is finalized and ready for use.'),
('rejected', 'The user has rejected the requirement.');

-- Main table for requirements
-- Stores the current state and content of each requirement.
CREATE TABLE requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- Unique ID for each requirement
    requirement_id VARCHAR(50) UNIQUE NOT NULL, -- e.g., 'R-001'
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL, -- The Markdown content of the requirement
    status_id INTEGER NOT NULL REFERENCES requirement_statuses(id),
    storage_url VARCHAR(500), -- Google Cloud Storage URL
    uploaded_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table to store a history of changes to a requirement
-- This is essential for the 'revised' status and for a full audit trail.
CREATE TABLE requirement_revisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirements(id) ON DELETE CASCADE,
    revision_number INT NOT NULL, -- e.g., 1, 2, 3... for each new version
    content_change TEXT, -- A diff or the full new content
    modified_by BIGINT REFERENCES users(id),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(requirement_id, revision_number) -- Ensure a unique revision number per requirement
);

-- Table to store the results of AI analysis
-- Each analysis is a separate record, linked to a specific requirement and its state.
CREATE TABLE ai_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirements(id) ON DELETE CASCADE,
    analysis_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    --
    -- AI-generated report details
    -- Using JSONB to store flexible, structured data from the AI
    --
    ai_quality_score INT, -- A score from 1-100
    ai_feedback JSONB    -- Stores a structured report of AI findings
    
    -- Example structure for ai_feedback JSONB:
    -- {
    --   "bugs": [{"text": "...", "reason": "..."}, {...}],
    --   "unclear_criteria": [{"text": "...", "suggestion": "..."}, {...}],
    --   "challenge_questions": [{"question": "...", "reason": "..."}, {...}]
    -- }
);

-- Create indexes for better performance
CREATE INDEX idx_requirements_status ON requirements(status_id);
CREATE INDEX idx_requirements_uploaded_by ON requirements(uploaded_by);
CREATE INDEX idx_requirements_created_at ON requirements(created_at);
CREATE INDEX idx_requirement_revisions_requirement_id ON requirement_revisions(requirement_id);
CREATE INDEX idx_ai_analyses_requirement_id ON ai_analyses(requirement_id);
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_users_project_id ON users(project_id);
