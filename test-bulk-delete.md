# Bulk Delete Requirements API Test Guide

## Overview
This guide shows how to test the new bulk delete requirements endpoint with async processing.

## API Endpoints

### 1. Bulk Delete Requirements (Async)
**POST** `/api/v1/requirements/bulk`

**Headers:**
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "requirementIds": [
    "123e4567-e89b-12d3-a456-************",
    "987fcdeb-51a2-43d1-b789-123456789abc"
  ],
  "userId": "user-uuid-here",
  "reason": "Cleanup of outdated requirements",
  "deleteFiles": true
}
```

**Response (202 Accepted):**
```json
{
  "jobId": "bulk_delete_123e4567-e89b-12d3-a456-************",
  "status": "queued",
  "message": "Bulk delete job created successfully. Use jobId to track progress.",
  "totalRequirements": 2,
  "estimatedProcessingTime": 45
}
```

### 2. Check Job Status
**GET** `/api/v1/requirements/bulk-delete/jobs/{jobId}/status`

**Response:**
```json
{
  "jobId": "bulk_delete_123e4567-e89b-12d3-a456-************",
  "status": "processing",
  "progress": {
    "step": "deleting_files",
    "percentage": 60,
    "description": "Deleting files and data (1/2)...",
    "processedCount": 1,
    "totalCount": 2,
    "updatedAt": "2024-01-15T10:35:00Z"
  },
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### 3. Cancel Job
**DELETE** `/api/v1/requirements/bulk-delete/jobs/{jobId}`

### 4. Retry Failed Job
**POST** `/api/v1/requirements/bulk-delete/jobs/{jobId}/retry`

### 5. Queue Statistics
**GET** `/api/v1/requirements/bulk-delete/queue/stats`

## Testing Steps

1. **Create some test requirements** first using the upload endpoint
2. **Get requirement IDs** from the list endpoint
3. **Submit bulk delete request** with those IDs
4. **Monitor progress** using the job status endpoint
5. **Verify deletion** by checking the list endpoint

## Example cURL Commands

```bash
# 1. Bulk delete
curl -X DELETE "http://localhost:3000/api/v1/requirements/bulk" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "requirementIds": ["req-id-1", "req-id-2"],
    "userId": "user-id",
    "reason": "Test cleanup",
    "deleteFiles": true
  }'

# 2. Check status
curl -X GET "http://localhost:3000/api/v1/requirements/bulk-delete/jobs/JOB_ID/status" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. Queue stats
curl -X GET "http://localhost:3000/api/v1/requirements/bulk-delete/queue/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Features

- ✅ Async processing with job queue
- ✅ Progress tracking with WebSocket support
- ✅ Automatic file deletion from Google Cloud Storage
- ✅ Batch processing with error handling
- ✅ Job retry and cancellation
- ✅ Detailed progress reporting
- ✅ Queue monitoring and statistics
