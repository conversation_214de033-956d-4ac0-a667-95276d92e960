# Bulk Delete Requirements API

## Overview

The Bulk Delete Requirements API provides asynchronous bulk deletion of requirement documents with progress tracking and file cleanup capabilities.

## Architecture

### Components

1. **BulkDeleteRequirementsDto** - Request validation and data transfer
2. **BulkDeleteQueueService** - Queue management for bulk delete jobs
3. **BulkDeleteProcessor** - Async job processing with progress tracking
4. **RequirementsController** - REST API endpoints
5. **RequirementsService** - Business logic coordination

### Queue System

- Uses **BullMQ** for reliable job processing
- Separate queue (`bulk-delete-queue`) for delete operations
- Automatic retry with exponential backoff
- Job persistence and monitoring via Bull Board

## API Endpoints

### 1. Bulk Delete Requirements
```
DELETE /api/v1/requirements/bulk
```

**Request Body:**
```typescript
{
  requirementIds: string[];     // Array of requirement UUIDs
  userId?: string;              // Optional user ID for audit
  reason?: string;              // Optional deletion reason
  deleteFiles?: boolean;        // Delete associated files (default: true)
}
```

**Response:**
```typescript
{
  jobId: string;                // Unique job identifier
  status: string;               // Initial status: "queued"
  message: string;              // Success message
  totalRequirements: number;    // Number of requirements to delete
  estimatedProcessingTime: number; // Estimated time in seconds
}
```

### 2. Job Status Tracking
```
GET /api/v1/requirements/bulk-delete/jobs/{jobId}/status
```

**Response:**
```typescript
{
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: {
    step: string;               // Current processing step
    percentage: number;         // Completion percentage (0-100)
    description: string;        // Human-readable description
    processedCount: number;     // Items processed so far
    totalCount: number;         // Total items to process
    updatedAt: Date;           // Last update timestamp
  };
  createdAt: Date;
  completedAt?: Date;
  errorMessage?: string;
}
```

### 3. Job Management
```
DELETE /api/v1/requirements/bulk-delete/jobs/{jobId}     # Cancel job
POST /api/v1/requirements/bulk-delete/jobs/{jobId}/retry # Retry failed job
GET /api/v1/requirements/bulk-delete/queue/stats         # Queue statistics
```

## Processing Steps

1. **Validation (10%)** - Verify requirements exist in database
2. **File Deletion (20-70%)** - Remove files from Google Cloud Storage
3. **Database Cleanup (70-90%)** - Delete records from database
4. **Completion (100%)** - Generate final report

## Features

### Async Processing
- Non-blocking API responses
- Background job processing
- Progress tracking with WebSocket support

### Error Handling
- Individual requirement error tracking
- Partial success reporting
- Automatic retry for failed jobs

### File Management
- Automatic cleanup of Google Cloud Storage files
- Configurable file deletion behavior
- Error handling for missing files

### Monitoring
- Real-time progress updates
- Queue statistics and monitoring
- Job history and logging

## Error Scenarios

### Partial Failures
- Some requirements not found: Continues with found requirements
- File deletion failures: Continues with database deletion
- Database errors: Reported per requirement

### Complete Failures
- Invalid request data: Immediate validation error
- Queue unavailable: Service unavailable error
- Critical system errors: Job marked as failed

## Security

- JWT authentication required
- User ID tracking for audit trails
- Deletion reason logging
- Authorization checks (implement as needed)

## Performance

- Batch processing for efficiency
- Configurable retry policies
- Queue-based load balancing
- Progress reporting without blocking

## Usage Examples

### Basic Bulk Delete
```javascript
const response = await fetch('/api/v1/requirements/bulk', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    requirementIds: ['uuid1', 'uuid2', 'uuid3'],
    userId: 'user-uuid',
    reason: 'Project cleanup',
    deleteFiles: true
  })
});

const { jobId } = await response.json();
```

### Progress Monitoring
```javascript
// Poll for status
const checkStatus = async () => {
  const response = await fetch(`/api/v1/requirements/bulk-delete/jobs/${jobId}/status`);
  const status = await response.json();
  
  console.log(`Progress: ${status.progress.percentage}%`);
  console.log(`Status: ${status.progress.description}`);
  
  if (status.status === 'completed') {
    console.log('Bulk delete completed!');
  } else if (status.status === 'failed') {
    console.error('Bulk delete failed:', status.errorMessage);
  }
};

// Or use WebSocket for real-time updates
const socket = io('/upload-progress');
socket.emit('subscribe-to-job', { jobId });
socket.on('job-status-update', (data) => {
  updateProgressBar(data.progress.percentage);
});
```

## Configuration

### Queue Settings
- Retry attempts: 3
- Backoff strategy: Exponential (2s base delay)
- Job retention: 50 completed, 20 failed
- Concurrency: Configurable per worker

### Processing Limits
- Minimum processing time: 30 seconds
- Estimated time per requirement: 5 seconds
- Maximum batch size: Configurable (recommended: 100)

## Monitoring and Debugging

### Bull Board Integration
- Access queue dashboard at `/queues`
- Monitor job status and performance
- View job details and logs
- Manual job management

### Logging
- Structured logging with job IDs
- Progress tracking logs
- Error details and stack traces
- Performance metrics
