# Async Upload API Documentation

## Overview

The async upload system provides a scalable, non-blocking file upload experience for SaaS applications. Instead of waiting for the entire upload and processing to complete, users get an immediate response with a job ID that can be used to track progress.

## Architecture

### Components

1. **Upload Queue (BullMQ)**: Manages background job processing
2. **Upload Processor**: <PERSON>les file upload and processing logic
3. **WebSocket Gateway**: Provides real-time progress updates
4. **Job Status API**: RESTful endpoints for job management

### Flow

```
1. POST /upload/async → Job ID (immediate response)
2. Background processing starts
3. Real-time updates via WebSocket
4. GET /jobs/{jobId}/status → Current status
5. Job completion notification
```

## API Endpoints

### 1. Async Upload

**Endpoint**: `POST /api/v1/requirements/upload/async`

**Description**: Upload a file asynchronously and get immediate job ID

**Request**:
```bash
curl -X POST "http://localhost:3002/api/v1/requirements/upload/async" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@document.pdf" \
  -F "name=Project Requirements" \
  -F "user_id=user123" \
  -F "company_id=company456" \
  -F "project_id=project789"
```

**Response**:
```json
{
  "jobId": "upload_123e4567-e89b-12d3-a456-426614174000",
  "status": "queued",
  "message": "Upload job created successfully. Use jobId to track progress.",
  "estimatedProcessingTime": 30
}
```

### 2. Job Status

**Endpoint**: `GET /api/v1/requirements/jobs/{jobId}/status`

**Description**: Get current status of an upload job

**Response**:
```json
{
  "jobId": "upload_123e4567-e89b-12d3-a456-426614174000",
  "status": "processing",
  "progress": {
    "currentStep": "uploading_to_storage",
    "percentage": 60,
    "description": "File uploaded to cloud storage successfully",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "createdAt": "2024-01-15T10:25:00Z",
  "completedAt": null,
  "error": null,
  "result": null
}
```

### 3. Cancel Job

**Endpoint**: `DELETE /api/v1/requirements/jobs/{jobId}`

**Description**: Cancel a pending or processing job

**Response**:
```json
{
  "message": "Job cancelled successfully"
}
```

### 4. Retry Job

**Endpoint**: `POST /api/v1/requirements/jobs/{jobId}/retry`

**Description**: Retry a failed job

**Response**:
```json
{
  "message": "Job retried successfully"
}
```

### 5. Queue Statistics

**Endpoint**: `GET /api/v1/requirements/queue/stats`

**Description**: Get upload queue statistics

**Response**:
```json
{
  "waiting": 5,
  "active": 2,
  "completed": 150,
  "failed": 3,
  "total": 160
}
```

## WebSocket Real-time Updates

### Connection

Connect to: `ws://localhost:3002/upload-progress`

### Events

#### Subscribe to Job Updates
```javascript
socket.emit('subscribe-to-job', { jobId: 'upload_123...' });
```

#### Receive Status Updates
```javascript
socket.on('job-status-update', (data) => {
  console.log('Progress:', data.progress.percentage + '%');
  console.log('Step:', data.progress.description);
});
```

#### Job Completion
```javascript
socket.on('job-completed', (data) => {
  console.log('Job completed:', data.result);
});
```

#### Job Failure
```javascript
socket.on('job-failed', (data) => {
  console.log('Job failed:', data.error);
});
```

## Job States

- **queued**: Job is waiting to be processed
- **processing**: Job is currently being processed
- **completed**: Job finished successfully
- **failed**: Job failed with an error
- **cancelled**: Job was cancelled by user

## Processing Steps

1. **validating** (0-15%): Validating upload data and user
2. **uploading_to_storage** (15-60%): Uploading file to cloud storage
3. **processing_content** (60-80%): Extracting and processing file content
4. **saving_to_database** (80-95%): Saving requirement to database
5. **generating_analysis** (95-99%): Running AI analysis (future)
6. **completed** (100%): All processing complete

## Error Handling

### Common Errors

- **File too large**: Maximum file size exceeded
- **Unsupported format**: File type not supported
- **Storage error**: Cloud storage upload failed
- **Database error**: Database save operation failed
- **Processing timeout**: Job took too long to complete

### Retry Logic

- Jobs automatically retry up to 3 times with exponential backoff
- Manual retry available for failed jobs
- Retry delay: 2s, 4s, 8s

## Monitoring

### Bull Board Dashboard

Access the queue monitoring dashboard at: `http://localhost:3002/queues`

Features:
- View active, waiting, completed, and failed jobs
- Retry failed jobs
- View job details and logs
- Monitor queue performance

## Frontend Integration Example

```javascript
// 1. Upload file
const uploadResponse = await fetch('/api/v1/requirements/upload/async', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: formData
});
const { jobId } = await uploadResponse.json();

// 2. Connect to WebSocket
const socket = io('/upload-progress');
socket.emit('subscribe-to-job', { jobId });

// 3. Handle progress updates
socket.on('job-status-update', (data) => {
  updateProgressBar(data.progress.percentage);
  updateStatusText(data.progress.description);
});

socket.on('job-completed', (data) => {
  showSuccess('Upload completed!');
  redirectToRequirement(data.result.id);
});

socket.on('job-failed', (data) => {
  showError(`Upload failed: ${data.error}`);
});
```

## Benefits

1. **Better UX**: Immediate response, real progress tracking
2. **Scalability**: Handle multiple large uploads concurrently
3. **Reliability**: Retry failed uploads, partial failure recovery
4. **Monitoring**: Track job status, queue health
5. **Resource Efficiency**: Non-blocking operations
