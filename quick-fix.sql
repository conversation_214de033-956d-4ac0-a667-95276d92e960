-- Quick fix: Insert requirement statuses manually
-- Run this SQL directly in your PostgreSQL database
-- NOTE: This is for the NEW schema with auto-increment ID and name column

-- First, drop the old table if it exists and recreate with new schema
DROP TABLE IF EXISTS requirement_statuses CASCADE;

CREATE TABLE requirement_statuses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT
);

INSERT INTO requirement_statuses (name, description) VALUES
('draft', 'The requirement has been uploaded but not yet processed.'),
('reviewed_by_ai', 'AI analysis is complete and awaiting user review.'),
('revised', 'The requirement has been modified by the user after AI review.'),
('approved', 'The user has approved the requirement.'),
('published', 'The requirement is finalized and ready for use.'),
('rejected', 'The user has rejected the requirement.')
ON CONFLICT (name) DO NOTHING;
